/**
 * Drag-and-Drop Upload Admin Options JavaScript
 * Handles the admin interface functionality for drag-drop upload configuration
 */

jQuery(document).ready(function($) {
    // Show/hide custom naming pattern field
    $('#dragdrop_file_naming').change(function() {
        if ($(this).val() === 'custom') {
            $('#custom_naming_pattern').show();
        } else {
            $('#custom_naming_pattern').hide();
        }
    });
    
    // Initialize the field visibility on page load
    if ($('#dragdrop_file_naming').val() === 'custom') {
        $('#custom_naming_pattern').show();
    } else {
        $('#custom_naming_pattern').hide();
    }
    
    // Add helpful tooltips and validation
    $('#dragdrop_max_files').on('input', function() {
        var value = parseInt($(this).val());
        if (value < 1) {
            $(this).val(1);
        } else if (value > 50) {
            $(this).val(50);
        }
    });
    
    // Validate file size format
    $('#dragdrop_max_file_size').on('blur', function() {
        var value = $(this).val().trim();
        var validFormat = /^\d+(\.\d+)?\s*(B|KB|MB|GB)$/i;
        
        if (value && !validFormat.test(value)) {
            $(this).addClass('error');
            $(this).attr('title', 'Invalid format. Use: 10MB, 5MB, 2048KB, etc.');
        } else {
            $(this).removeClass('error');
            $(this).removeAttr('title');
        }
    });
    
    // Add visual feedback for file type input
    $('#dragdrop_allowed_types').on('input', function() {
        var value = $(this).val();
        var hasWildcard = value.includes('*');
        var hasExtension = value.includes('.');
        var hasMimeType = value.includes('/');
        
        if (hasWildcard || hasExtension || hasMimeType) {
            $(this).removeClass('error');
        }
    });
});
