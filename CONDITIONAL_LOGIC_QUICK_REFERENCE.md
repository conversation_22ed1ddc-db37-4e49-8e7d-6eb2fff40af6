# Conditional Logic - Quick Reference Guide

## 🚀 Quick Start

### 1. Access the Feature
1. Go to **Contact > Contact Forms**
2. Edit any form
3. Click **"Conditional Logic"** tab
4. Click **"Add Rule Group"**

### 2. Create a Rule
```
Rule Group #1
├── Target Field: [field-to-control]
├── Action: [Show/Hide]
├── Logic: [AND/OR]
└── Conditions: [field] [operator] [value]
```

## 📋 Supported Operators

| Operator | Description | Example |
|----------|-------------|---------|
| `equals` | Exact match | name equals "John" |
| `does not equal` | Not equal | status ≠ "pending" |
| `contains` | Contains substring | email contains "@gmail" |
| `does not contain` | Doesn't contain | message ∌ "spam" |
| `starts with` | Begins with | phone starts with "+1" |
| `ends with` | Ends with | website ends with ".com" |
| `is greater than` | Numeric > | age > 18 |
| `is less than` | Numeric < | price < 100 |
| `is empty` | No value | field is empty |
| `is not empty` | Has value | field has content |

## 🔗 Logic Types

### AND Logic (All conditions match)
```javascript
Show "Business Details" if:
✓ User Type = "Business" AND
✓ Country = "USA" AND  
✓ Revenue > "50000"
```

### OR Logic (Any condition matches)
```javascript
Show "Discount Field" if:
✓ Age < 25 OR
✓ Status = "Student" OR
✓ Member = "Premium"
```

## 🎯 Field Types Supported

### Input Fields
- Text: `[text field-name]`
- Email: `[email email-field]`
- Number: `[number quantity]`
- Date: `[date event-date]`
- Textarea: `[textarea message]`

### Selection Fields
- Dropdown: `[select menu "Option 1" "Option 2"]`
- Radio: `[radio choice "Yes" "No"]`
- Checkbox: `[checkbox options "A" "B" "C"]`

### Special Fields
- File Upload: `[file upload-field]`
- Acceptance: `[acceptance terms]`

## 🛠️ Common Use Cases

### 1. Show Additional Info for Business Users
```
Target: business-details
Action: Show
Logic: All conditions match
Condition: user-type equals "business"
```

### 2. Hide Field Unless Specific Option Selected
```
Target: other-details
Action: Show
Logic: All conditions match
Condition: other-option equals "yes"
```

### 3. Multi-Step Form Logic
```
Target: step-2
Action: Show
Logic: All conditions match
Conditions:
- name is not empty
- email is not empty
- phone is not empty
```

### 4. Conditional Required Fields
```
Target: company-info
Action: Show
Logic: Any condition matches
Conditions:
- account-type equals "business"
- purchase-amount is greater than "1000"
```

## 🐛 Troubleshooting

### Rules Not Working?
1. **Check Browser Console** (F12) for errors
2. **Verify Field Names** match exactly
3. **Test API Endpoint** manually
4. **Check for CSS Conflicts**

### Common Console Messages
```javascript
✅ "Found X CF7 forms to initialize"
✅ "Evaluating X rule groups"
✅ "Condition result: true/false"
❌ "No conditional logic rules found"
❌ "Field not found: field-name"
```

### Debug Mode
```javascript
// Enable in browser console
localStorage.setItem('laAddonsDebug', 'true');
// Reload page for detailed logs
```

## 🔧 Technical Details

### Data Storage
- **Location**: WordPress post meta
- **Key**: `_la_addons_conditional_logic`
- **Format**: PHP serialized array

### API Endpoints
```
GET /wp-json/la-addons/v1/forms/{id}/conditional-logic
POST /wp-admin/admin-ajax.php (action: la_addons_get_conditional_logic)
```

### CSS Classes
```css
.la-addons-hidden { display: none !important; }
.la-addons-visible { display: block !important; }
```

### JavaScript Events
- `change` - Select/radio changes
- `input` - Text input changes
- `keyup` - Keyboard input
- Debounced: 100ms delay

## 📱 Browser Support

| Browser | Version | Status |
|---------|---------|--------|
| Chrome | 60+ | ✅ Full Support |
| Firefox | 55+ | ✅ Full Support |
| Safari | 12+ | ✅ Full Support |
| Edge | 79+ | ✅ Full Support |
| Opera | 47+ | ✅ Full Support |

## 🎨 Customization

### Custom CSS Animations
```css
.la-addons-hidden {
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s ease;
}

.la-addons-visible {
    opacity: 1;
    transform: translateY(0);
    transition: all 0.3s ease;
}
```

### WordPress Hooks
```php
// Filter form fields
apply_filters('la_addons_conditional_form_fields', $fields, $form_id);

// Filter rules before save
apply_filters('la_addons_conditional_logic_rules', $rules, $form_id);

// Action after save
do_action('la_addons_conditional_logic_saved', $form_id, $rules);
```

## 📊 Performance Tips

1. **Keep Rules Simple** - Avoid overly complex conditions
2. **Limit Rule Groups** - Use fewer groups when possible
3. **Optimize Field Names** - Use specific, unique names
4. **Test Regularly** - Verify performance with real data
5. **Monitor Console** - Watch for performance warnings

## 🔒 Security Features

- ✅ WordPress nonce verification
- ✅ Input sanitization
- ✅ XSS prevention
- ✅ SQL injection protection
- ✅ Capability checks for admin access

## 📞 Support

### Self-Help
1. Check this documentation
2. Review browser console
3. Test with simple rules first
4. Use the test file: `/tests/conditional-fields-test.html`

### Getting Help
- WordPress.org forums
- GitHub issues
- Stack Overflow (tags: `contact-form-7`, `conditional-logic`)

---

**Quick Tip**: Start with simple rules and gradually add complexity. Test each rule individually before combining multiple conditions.

**Remember**: Field names must match exactly between your form and the conditional logic rules. Use the dropdown menus in the admin interface to ensure accuracy.
