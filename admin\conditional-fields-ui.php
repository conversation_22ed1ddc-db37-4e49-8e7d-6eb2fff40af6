<?php

function la_addons_add_conditional_logic_tab( $panels ) {
    $panels['conditional-logic-panel'] = array(
        'title' => __( 'Conditional Logic', 'la-addons' ),
        'callback' => 'la_addons_conditional_logic_panel_content'
    );
    return $panels;
}

add_filter( 'wpcf7_editor_panels', 'la_addons_add_conditional_logic_tab' );

function la_addons_conditional_logic_panel_content( $post ) {
    $rules = get_post_meta( $post->id(), '_la_addons_conditional_logic', true );
    if (empty($rules)) {
        $rules = array();
    }
    
    // Get all form fields with proper detection
    $form_fields = array();
    
    // Method 1: Use CF7's built-in scan_form_tags
    $form_tags = $post->scan_form_tags();
    foreach ($form_tags as $tag) {
        if (!empty($tag['name'])) {
            $form_fields[$tag['name']] = $tag;
        }
    }
    
    // Method 2: If no fields detected, try our custom scanner
    if (empty($form_fields)) {
        $custom_fields = la_addons_scan_form_for_fields($post->id());
        if (!empty($custom_fields)) {
            $form_fields = $custom_fields;
        }
    }
    
    // Method 3: Last resort - parse the form content directly
    if (empty($form_fields)) {
        $form_content = $post->prop('form');
        preg_match_all('/\[([a-zA-Z0-9_-]+)\s+([^\]]*)\]/', $form_content, $matches, PREG_SET_ORDER);
        
        foreach ($matches as $match) {
            if (isset($match[2]) && preg_match('/name="([^"]+)"/', $match[2], $name_match)) {
                $field_name = $name_match[1];
                $form_fields[$field_name] = array(
                    'name' => $field_name,
                    'type' => isset($match[1]) ? $match[1] : 'text',
                    'basetype' => isset($match[1]) ? $match[1] : 'text',
                    'options' => array()
                );
            }
        }
        
        error_log('Alternative field detection: ' . print_r($form_fields, true));
    }
    
    // Debug output
    error_log('Detected form fields: ' . print_r(array_keys($form_fields), true));
    ?>
    <h2><?php echo esc_html__( 'Conditional Logic', 'la-addons' ); ?></h2>
    <p><?php echo esc_html__( 'Create rules to show or hide fields based on user input. Multiple conditions can be combined with AND/OR logic.', 'la-addons' ); ?></p>
    
    <?php if (empty($form_fields)): ?>
    <div class="notice notice-warning">
        <p><?php echo esc_html__('No form fields detected. Please add some fields to your form before setting up conditional logic.', 'la-addons'); ?></p>
    </div>
    <?php endif; ?>
    
    <div id="la-addons-conditional-logic-builder" class="la-addons-panel">
        <div class="rules-container">
            <?php if (empty($rules)): ?>
                <div class="no-rules-message">
                    <p><?php echo esc_html__('No conditional rules have been created yet. Click "Add Rule Group" to create your first rule.', 'la-addons'); ?></p>
                </div>
            <?php else: ?>
                <?php foreach ($rules as $group_index => $rule_group): ?>
                    <div class="rule-group" data-group-index="<?php echo esc_attr($group_index); ?>">
                        <div class="rule-group-header">
                            <h3><?php echo esc_html__('Rule Group', 'la-addons'); ?> #<?php echo esc_html($group_index + 1); ?></h3>
                            <button type="button" class="button delete-rule-group"><?php echo esc_html__('Delete Group', 'la-addons'); ?></button>
                        </div>
                        
                        <div class="target-field-container">
                            <label><?php echo esc_html__('Show/Hide Field:', 'la-addons'); ?></label>
                            <select name="la_addons_conditional_logic[<?php echo esc_attr($group_index); ?>][target_field]" class="target-field-select">
                                <?php foreach ($form_fields as $field_name => $field): ?>
                                    <option value="<?php echo esc_attr($field_name); ?>" <?php selected($rule_group['target_field'], $field_name); ?>>
                                        <?php echo esc_html($field_name); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            
                            <select name="la_addons_conditional_logic[<?php echo esc_attr($group_index); ?>][action]" class="action-select">
                                <option value="show" <?php selected($rule_group['action'], 'show'); ?>><?php echo esc_html__('Show', 'la-addons'); ?></option>
                                <option value="hide" <?php selected($rule_group['action'], 'hide'); ?>><?php echo esc_html__('Hide', 'la-addons'); ?></option>
                            </select>
                            
                            <label><?php echo esc_html__('if', 'la-addons'); ?></label>
                            
                            <select name="la_addons_conditional_logic[<?php echo esc_attr($group_index); ?>][logic]" class="logic-select">
                                <option value="all" <?php selected($rule_group['logic'], 'all'); ?>><?php echo esc_html__('All conditions match (AND)', 'la-addons'); ?></option>
                                <option value="any" <?php selected($rule_group['logic'], 'any'); ?>><?php echo esc_html__('Any condition matches (OR)', 'la-addons'); ?></option>
                            </select>
                        </div>
                        
                        <div class="conditions-container">
                            <?php foreach ($rule_group['conditions'] as $condition_index => $condition): ?>
                                <div class="condition-row" data-condition-index="<?php echo esc_attr($condition_index); ?>">
                                    <select name="la_addons_conditional_logic[<?php echo esc_attr($group_index); ?>][conditions][<?php echo esc_attr($condition_index); ?>][field]" class="condition-field-select">
                                        <?php foreach ($form_fields as $field_name => $field): ?>
                                            <option value="<?php echo esc_attr($field_name); ?>" <?php selected($condition['field'], $field_name); ?>>
                                                <?php echo esc_html($field_name); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                    
                                    <select name="la_addons_conditional_logic[<?php echo esc_attr($group_index); ?>][conditions][<?php echo esc_attr($condition_index); ?>][operator]" class="operator-select">
                                        <option value="equal" <?php selected($condition['operator'], 'equal'); ?>><?php echo esc_html__('equals', 'la-addons'); ?></option>
                                        <option value="not_equal" <?php selected($condition['operator'], 'not_equal'); ?>><?php echo esc_html__('does not equal', 'la-addons'); ?></option>
                                        <option value="contains" <?php selected($condition['operator'], 'contains'); ?>><?php echo esc_html__('contains', 'la-addons'); ?></option>
                                        <option value="not_contains" <?php selected($condition['operator'], 'not_contains'); ?>><?php echo esc_html__('does not contain', 'la-addons'); ?></option>
                                        <option value="greater_than" <?php selected($condition['operator'], 'greater_than'); ?>><?php echo esc_html__('is greater than', 'la-addons'); ?></option>
                                        <option value="less_than" <?php selected($condition['operator'], 'less_than'); ?>><?php echo esc_html__('is less than', 'la-addons'); ?></option>
                                        <option value="starts_with" <?php selected($condition['operator'], 'starts_with'); ?>><?php echo esc_html__('starts with', 'la-addons'); ?></option>
                                        <option value="ends_with" <?php selected($condition['operator'], 'ends_with'); ?>><?php echo esc_html__('ends with', 'la-addons'); ?></option>
                                        <option value="is_empty" <?php selected($condition['operator'], 'is_empty'); ?>><?php echo esc_html__('is empty', 'la-addons'); ?></option>
                                        <option value="is_not_empty" <?php selected($condition['operator'], 'is_not_empty'); ?>><?php echo esc_html__('is not empty', 'la-addons'); ?></option>
                                    </select>
                                    
                                    <input type="text" 
                                           name="la_addons_conditional_logic[<?php echo esc_attr($group_index); ?>][conditions][<?php echo esc_attr($condition_index); ?>][value]" 
                                           value="<?php echo esc_attr($condition['value']); ?>"
                                           class="condition-value-input"
                                           <?php echo in_array($condition['operator'], array('is_empty', 'is_not_empty')) ? 'style="display:none;"' : ''; ?>>
                                    
                                    <button type="button" class="button delete-condition"><?php echo esc_html__('Remove', 'la-addons'); ?></button>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        
                        <button type="button" class="button add-condition"><?php echo esc_html__('Add Condition', 'la-addons'); ?></button>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
        
        <div class="rule-actions">
            <button type="button" class="button button-secondary" id="la-addons-add-rule-group"><?php echo esc_html__('Add Rule Group', 'la-addons'); ?></button>
        </div>
    </div>

    <div id="rule-templates" style="display: none;">
        <div class="rule-group-template">
            <div class="rule-group" data-group-index="{{GROUP_INDEX}}">
                <div class="rule-group-header">
                    <h3><?php echo esc_html__('Rule Group', 'la-addons'); ?> #{{GROUP_NUMBER}}</h3>
                    <button type="button" class="button delete-rule-group"><?php echo esc_html__('Delete Group', 'la-addons'); ?></button>
                </div>
                
                <div class="target-field-container">
                    <label><?php echo esc_html__('Show/Hide Field:', 'la-addons'); ?></label>
                    <select name="la_addons_conditional_logic[{{GROUP_INDEX}}][target_field]" class="target-field-select" disabled>
                        <?php foreach ($form_fields as $field_name => $field): ?>
                            <option value="<?php echo esc_attr($field_name); ?>"><?php echo esc_html($field_name); ?></option>
                        <?php endforeach; ?>
                    </select>

                    <select name="la_addons_conditional_logic[{{GROUP_INDEX}}][action]" class="action-select" disabled>
                        <option value="show"><?php echo esc_html__('Show', 'la-addons'); ?></option>
                        <option value="hide"><?php echo esc_html__('Hide', 'la-addons'); ?></option>
                    </select>

                    <label><?php echo esc_html__('if', 'la-addons'); ?></label>

                    <select name="la_addons_conditional_logic[{{GROUP_INDEX}}][logic]" class="logic-select" disabled>
                        <option value="all"><?php echo esc_html__('All conditions match (AND)', 'la-addons'); ?></option>
                        <option value="any"><?php echo esc_html__('Any condition matches (OR)', 'la-addons'); ?></option>
                    </select>
                </div>
                
                <div class="conditions-container">
                    <!-- Conditions will be added here -->
                </div>
                
                <button type="button" class="button add-condition"><?php echo esc_html__('Add Condition', 'la-addons'); ?></button>
            </div>
        </div>
        
        <div class="condition-template">
            <div class="condition-row" data-condition-index="{{CONDITION_INDEX}}">
                <select name="la_addons_conditional_logic[{{GROUP_INDEX}}][conditions][{{CONDITION_INDEX}}][field]" class="condition-field-select" disabled>
                    <?php foreach ($form_fields as $field_name => $field): ?>
                        <option value="<?php echo esc_attr($field_name); ?>"><?php echo esc_html($field_name); ?></option>
                    <?php endforeach; ?>
                </select>

                <select name="la_addons_conditional_logic[{{GROUP_INDEX}}][conditions][{{CONDITION_INDEX}}][operator]" class="operator-select" disabled>
                    <option value="equal"><?php echo esc_html__('equals', 'la-addons'); ?></option>
                    <option value="not_equal"><?php echo esc_html__('does not equal', 'la-addons'); ?></option>
                    <option value="contains"><?php echo esc_html__('contains', 'la-addons'); ?></option>
                    <option value="not_contains"><?php echo esc_html__('does not contain', 'la-addons'); ?></option>
                    <option value="greater_than"><?php echo esc_html__('is greater than', 'la-addons'); ?></option>
                    <option value="less_than"><?php echo esc_html__('is less than', 'la-addons'); ?></option>
                    <option value="starts_with"><?php echo esc_html__('starts with', 'la-addons'); ?></option>
                    <option value="ends_with"><?php echo esc_html__('ends with', 'la-addons'); ?></option>
                    <option value="is_empty"><?php echo esc_html__('is empty', 'la-addons'); ?></option>
                    <option value="is_not_empty"><?php echo esc_html__('is not empty', 'la-addons'); ?></option>
                </select>

                <input type="text" name="la_addons_conditional_logic[{{GROUP_INDEX}}][conditions][{{CONDITION_INDEX}}][value]" value="" class="condition-value-input" disabled>

                <button type="button" class="button delete-condition"><?php echo esc_html__('Remove', 'la-addons'); ?></button>
            </div>
        </div>
    </div>

    <?php
    // Get all form fields for JavaScript with detailed information
    $field_data = array();
    
    foreach ($form_fields as $field_name => $field) {
        // Extract options for select, checkbox, and radio fields
        $options = array();
        
        if (isset($field['options']) && is_array($field['options'])) {
            foreach ($field['options'] as $option) {
                // Check if option has explicit value
                if (preg_match('/^([^:]+):(.+)$/', $option, $matches)) {
                    $options[] = $matches[2]; // Use the value after the colon
                } else {
                    $options[] = $option; // Use the whole option
                }
            }
        }
        
        $field_data[] = array(
            'name' => $field_name,
            'type' => isset($field['type']) ? $field['type'] : 'text',
            'basetype' => isset($field['basetype']) ? $field['basetype'] : 'text',
            'options' => $options
        );
    }
    
    // Debug output
    error_log('Form fields for conditional logic JS: ' . print_r($field_data, true));
    
    // Add a debug output to the page (only visible to admins)
    if (current_user_can('manage_options')) {
        echo '<div class="la-addons-debug" style="margin-top: 20px; padding: 10px; background: #f7f7f7; border-left: 4px solid #0073aa;">';
        echo '<h4>Debug Information (Only visible to admins)</h4>';
        echo '<p>Detected Fields: ' . count($field_data) . '</p>';
        echo '<ul>';
        foreach ($field_data as $field) {
            echo '<li>' . esc_html($field['name']) . ' (Type: ' . esc_html($field['basetype']) . ')</li>';
        }
        echo '</ul>';
        echo '</div>';
    }
    
    wp_localize_script('la-addons-conditional-fields', 'laAddonsConditionalFields', array(
        'fields' => $field_data,
        'i18n' => array(
            'addCondition' => __('Add Condition', 'la-addons'),
            'removeCondition' => __('Remove', 'la-addons'),
            'addRuleGroup' => __('Add Rule Group', 'la-addons'),
            'deleteRuleGroup' => __('Delete Group', 'la-addons'),
            'ruleGroup' => __('Rule Group', 'la-addons'),
            'noRules' => __('No conditional rules have been created yet.', 'la-addons')
        )
    ));
    ?>
    <?php
}

function la_addons_save_conditional_logic($post) {
    if (isset($_POST['la_addons_conditional_logic'])) {
        $rules = $_POST['la_addons_conditional_logic'];

        // Debug: Log what's being submitted
        error_log('Conditional logic rules submitted: ' . print_r($rules, true));

        // Sanitize the input
        $sanitized_rules = array();
        foreach ($rules as $group_index => $rule_group) {
            // Skip if rule group is not an array or is empty
            if (!is_array($rule_group) || empty($rule_group)) {
                error_log("Skipping empty or invalid rule group at index $group_index");
                continue;
            }

            // Skip if no target field is selected or target field is empty
            if (empty($rule_group['target_field']) || trim($rule_group['target_field']) === '') {
                error_log("Skipping rule group at index $group_index - no target field");
                continue;
            }
            
            $sanitized_group = array(
                'target_field' => sanitize_text_field($rule_group['target_field']),
                'action' => in_array($rule_group['action'], array('show', 'hide')) ? $rule_group['action'] : 'show',
                'logic' => in_array($rule_group['logic'], array('all', 'any')) ? $rule_group['logic'] : 'all',
                'conditions' => array()
            );
            
            if (isset($rule_group['conditions']) && is_array($rule_group['conditions'])) {
                foreach ($rule_group['conditions'] as $condition_index => $condition) {
                    // Skip if condition is not an array or is empty
                    if (!is_array($condition) || empty($condition)) {
                        error_log("Skipping empty or invalid condition at index $condition_index in group $group_index");
                        continue;
                    }

                    // Skip if no field is selected or field is empty
                    if (empty($condition['field']) || trim($condition['field']) === '') {
                        error_log("Skipping condition at index $condition_index in group $group_index - no field");
                        continue;
                    }

                    // Skip if no operator is selected
                    if (empty($condition['operator']) || trim($condition['operator']) === '') {
                        error_log("Skipping condition at index $condition_index in group $group_index - no operator");
                        continue;
                    }

                    $sanitized_condition = array(
                        'field' => sanitize_text_field($condition['field']),
                        'operator' => sanitize_text_field($condition['operator']),
                        'value' => sanitize_text_field($condition['value'])
                    );

                    $sanitized_group['conditions'][] = $sanitized_condition;
                }
            }

            // Only add the group if it has at least one valid condition
            if (!empty($sanitized_group['conditions'])) {
                $sanitized_rules[] = $sanitized_group;
                error_log("Added valid rule group with " . count($sanitized_group['conditions']) . " conditions");
            } else {
                error_log("Skipping rule group at index $group_index - no valid conditions");
            }
        }

        error_log('Final sanitized rules to be saved: ' . print_r($sanitized_rules, true));
        update_post_meta($post->id(), '_la_addons_conditional_logic', $sanitized_rules);
    } else {
        // If no rules are submitted, clear any existing rules
        delete_post_meta($post->id(), '_la_addons_conditional_logic');
    }
}

add_action('wpcf7_save_contact_form', 'la_addons_save_conditional_logic');




