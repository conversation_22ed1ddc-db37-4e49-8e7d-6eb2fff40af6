<?php
/**
 * Plugin Name:       Orbit Addons for CF7
 * Plugin URI:        https://github.com/sadmankabiir/la-addons
 * Description:       A comprehensive, all-in-one solution for managing form submissions, creating dynamic forms with conditional logic, capturing digital signatures, and seamlessly integrating with the WordPress Media Library.
 * Version:           1.0
 * Requires at least: 5.2
 * Requires PHP:      7.2
 * Author:            <PERSON><PERSON>
 * Author URI:        https://github.com/sadmankabiir
 * License:           GPL v2 or later
 * License URI:       https://www.gnu.org/licenses/gpl-2.0.html
 * Update URI:        https://github.com/sadmankabiir/la-addons
 * Text Domain:       la-addons
 * Domain Path:       /languages
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Define plugin constants
define( 'LA_ADDONS_VERSION', '1.0' );
define( 'LA_ADDONS_PLUGIN_DIR', plugin_dir_path( __FILE__ ) );
define( 'LA_ADDONS_PLUGIN_URL', plugin_dir_url( __FILE__ ) );

// --- FILE INCLUDES ---
require_once LA_ADDONS_PLUGIN_DIR . 'includes/database.php';
require_once LA_ADDONS_PLUGIN_DIR . 'includes/submission-handler.php';
require_once LA_ADDONS_PLUGIN_DIR . 'includes/enqueue-scripts.php';
require_once LA_ADDONS_PLUGIN_DIR . 'includes/digital-signature.php';
require_once LA_ADDONS_PLUGIN_DIR . 'includes/conditional-fields-api.php';
require_once LA_ADDONS_PLUGIN_DIR . 'includes/drag-drop-upload.php';

// --- ADMIN-ONLY INCLUDES ---
if ( is_admin() ) {
    require_once LA_ADDONS_PLUGIN_DIR . 'admin/admin-menu.php';
    require_once LA_ADDONS_PLUGIN_DIR . 'admin/conditional-fields-ui.php';
    require_once LA_ADDONS_PLUGIN_DIR . 'admin/signature-options.php';
    require_once LA_ADDONS_PLUGIN_DIR . 'admin/dragdrop-upload-options.php';
}

/**
 * Check if Contact Form 7 is active
 */
function la_addons_check_cf7_dependency() {
    if ( ! function_exists( 'is_plugin_active' ) ) {
        include_once( ABSPATH . 'wp-admin/includes/plugin.php' );
    }

    if ( ! is_plugin_active( 'contact-form-7/wp-contact-form-7.php' ) ) {
        add_action( 'admin_notices', 'la_addons_cf7_missing_notice' );
        return false;
    }

    return true;
}

/**
 * Display admin notice if CF7 is missing
 */
function la_addons_cf7_missing_notice() {
    echo '<div class="notice notice-error"><p>';
    echo '<strong>LA Addons:</strong> Contact Form 7 plugin is required for this plugin to work properly. ';
    echo 'Please install and activate Contact Form 7.';
    echo '</p></div>';
}

// Check CF7 dependency on plugin load
add_action( 'plugins_loaded', 'la_addons_check_cf7_dependency' );

// --- ACTIVATION HOOK ---
register_activation_hook( __FILE__, 'la_addons_activate' );




