<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Drag & Drop Upload Test - LA Addons</title>
    <link rel="stylesheet" href="../assets/css/admin-style.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f0f0f1;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        
        .test-section {
            margin-bottom: 40px;
            padding-bottom: 30px;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .test-section:last-child {
            border-bottom: none;
        }
        
        h1 {
            color: #1f2937;
            margin-bottom: 10px;
        }
        
        h2 {
            color: #374151;
            margin-bottom: 20px;
            font-size: 18px;
        }
        
        .test-description {
            color: #6b7280;
            margin-bottom: 20px;
            line-height: 1.5;
        }
        
        .form-example {
            background-color: #f9fafb;
            padding: 20px;
            border-radius: 6px;
            border: 1px solid #e5e7eb;
        }
        
        .wpcf7-form {
            max-width: none;
        }
        
        .form-field {
            margin-bottom: 20px;
        }
        
        .form-field label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #374151;
        }
        
        .form-field input[type="text"],
        .form-field input[type="email"],
        .form-field textarea,
        .form-field select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .form-field textarea {
            height: 80px;
            resize: vertical;
        }
        
        .submit-button {
            background-color: #3b82f6;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
        }
        
        .submit-button:hover {
            background-color: #2563eb;
        }
        
        .test-info {
            background-color: #eff6ff;
            border: 1px solid #bfdbfe;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        
        .test-info h3 {
            margin: 0 0 10px 0;
            color: #1e40af;
            font-size: 16px;
        }
        
        .test-info ul {
            margin: 0;
            padding-left: 20px;
            color: #1e40af;
        }
        
        .responsive-test {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }
        
        @media (max-width: 768px) {
            .responsive-test {
                grid-template-columns: 1fr;
            }
        }
        
        .device-frame {
            border: 2px solid #d1d5db;
            border-radius: 8px;
            padding: 15px;
            background-color: #ffffff;
        }
        
        .device-frame h4 {
            margin: 0 0 15px 0;
            color: #374151;
            text-align: center;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🚀 Drag & Drop Upload Test Suite</h1>
        <p class="test-description">
            This test page demonstrates the drag-and-drop multiple file upload functionality 
            integrated with Contact Form 7. Test various scenarios to ensure everything works correctly.
        </p>

        <!-- Basic Upload Test -->
        <div class="test-section">
            <h2>1. Basic Drag & Drop Upload</h2>
            <div class="test-info">
                <h3>Test Instructions:</h3>
                <ul>
                    <li>Drag files from your desktop onto the upload zone</li>
                    <li>Click "browse" to select files using the file dialog</li>
                    <li>Try uploading different file types (images, PDFs, documents)</li>
                    <li>Verify file previews and thumbnails display correctly</li>
                </ul>
            </div>
            
            <div class="form-example">
                <form class="wpcf7-form">
                    <input type="hidden" name="_wpcf7" value="123">
                    
                    <div class="form-field">
                        <label>Upload Files (Max 5 files, 10MB each):</label>
                        <div class="la-addons-dragdrop-upload" 
                             data-form-id="123" 
                             data-field-name="basic-upload"
                             data-max-files="5"
                             data-max-size="10MB"
                             data-accepted-types="image/*,application/pdf,.doc,.docx,.txt">
                            
                            <div class="la-dragdrop-zone">
                                <div class="la-dragdrop-content">
                                    <div class="la-dragdrop-icon">
                                        <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                            <polyline points="7,10 12,15 17,10"></polyline>
                                            <line x1="12" y1="15" x2="12" y2="3"></line>
                                        </svg>
                                    </div>
                                    <div class="la-dragdrop-text">
                                        <p class="la-dragdrop-primary">Drop files here or <button type="button" class="la-dragdrop-browse">browse</button></p>
                                        <p class="la-dragdrop-secondary">Maximum 5 files, 10MB each</p>
                                    </div>
                                </div>
                                <input type="file" name="basic-upload[]" multiple accept="image/*,application/pdf,.doc,.docx,.txt" style="display: none;">
                            </div>
                            <div class="la-dragdrop-files"></div>
                            <div class="la-dragdrop-progress" style="display: none;">
                                <div class="la-progress-bar">
                                    <div class="la-progress-fill"></div>
                                </div>
                                <div class="la-progress-text">Uploading...</div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Images Only Test -->
        <div class="test-section">
            <h2>2. Images Only Upload</h2>
            <div class="test-info">
                <h3>Test Instructions:</h3>
                <ul>
                    <li>Try uploading only image files (JPG, PNG, GIF, WebP)</li>
                    <li>Verify image thumbnails are generated</li>
                    <li>Test that non-image files are rejected</li>
                </ul>
            </div>
            
            <div class="form-example">
                <form class="wpcf7-form">
                    <input type="hidden" name="_wpcf7" value="123">
                    
                    <div class="form-field">
                        <label>Upload Images Only (Max 10 files, 5MB each):</label>
                        <div class="la-addons-dragdrop-upload" 
                             data-form-id="123" 
                             data-field-name="images-upload"
                             data-max-files="10"
                             data-max-size="5MB"
                             data-accepted-types="image/*">
                            
                            <div class="la-dragdrop-zone">
                                <div class="la-dragdrop-content">
                                    <div class="la-dragdrop-icon">
                                        <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                                            <circle cx="8.5" cy="8.5" r="1.5"/>
                                            <polyline points="21,15 16,10 5,21"/>
                                        </svg>
                                    </div>
                                    <div class="la-dragdrop-text">
                                        <p class="la-dragdrop-primary">Drop images here or <button type="button" class="la-dragdrop-browse">browse</button></p>
                                        <p class="la-dragdrop-secondary">Images only, maximum 10 files, 5MB each</p>
                                    </div>
                                </div>
                                <input type="file" name="images-upload[]" multiple accept="image/*" style="display: none;">
                            </div>
                            <div class="la-dragdrop-files"></div>
                            <div class="la-dragdrop-progress" style="display: none;">
                                <div class="la-progress-bar">
                                    <div class="la-progress-fill"></div>
                                </div>
                                <div class="la-progress-text">Uploading...</div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Complete Form Test -->
        <div class="test-section">
            <h2>3. Complete Form Integration</h2>
            <div class="test-info">
                <h3>Test Instructions:</h3>
                <ul>
                    <li>Fill out the entire form including file uploads</li>
                    <li>Test form submission with uploaded files</li>
                    <li>Verify all form data is processed correctly</li>
                </ul>
            </div>
            
            <div class="form-example">
                <form class="wpcf7-form">
                    <input type="hidden" name="_wpcf7" value="123">
                    
                    <div class="form-field">
                        <label for="contact-name">Name *</label>
                        <input type="text" id="contact-name" name="contact-name" required>
                    </div>
                    
                    <div class="form-field">
                        <label for="contact-email">Email *</label>
                        <input type="email" id="contact-email" name="contact-email" required>
                    </div>
                    
                    <div class="form-field">
                        <label for="contact-subject">Subject</label>
                        <input type="text" id="contact-subject" name="contact-subject">
                    </div>
                    
                    <div class="form-field">
                        <label for="contact-message">Message</label>
                        <textarea id="contact-message" name="contact-message"></textarea>
                    </div>
                    
                    <div class="form-field">
                        <label>Attach Documents:</label>
                        <div class="la-addons-dragdrop-upload" 
                             data-form-id="123" 
                             data-field-name="documents"
                             data-max-files="3"
                             data-max-size="10MB"
                             data-accepted-types="application/pdf,.doc,.docx,.txt,.rtf">
                            
                            <div class="la-dragdrop-zone">
                                <div class="la-dragdrop-content">
                                    <div class="la-dragdrop-icon">
                                        <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                                        </svg>
                                    </div>
                                    <div class="la-dragdrop-text">
                                        <p class="la-dragdrop-primary">Drop documents here or <button type="button" class="la-dragdrop-browse">browse</button></p>
                                        <p class="la-dragdrop-secondary">PDF, DOC, DOCX, TXT files only, maximum 3 files, 10MB each</p>
                                    </div>
                                </div>
                                <input type="file" name="documents[]" multiple accept="application/pdf,.doc,.docx,.txt,.rtf" style="display: none;">
                            </div>
                            <div class="la-dragdrop-files"></div>
                            <div class="la-dragdrop-progress" style="display: none;">
                                <div class="la-progress-bar">
                                    <div class="la-progress-fill"></div>
                                </div>
                                <div class="la-progress-text">Uploading...</div>
                            </div>
                        </div>
                    </div>
                    
                    <button type="submit" class="submit-button">Send Message</button>
                </form>
            </div>
        </div>

        <!-- Responsive Test -->
        <div class="test-section">
            <h2>4. Responsive Design Test</h2>
            <div class="test-info">
                <h3>Test Instructions:</h3>
                <ul>
                    <li>Resize your browser window to test mobile responsiveness</li>
                    <li>Test touch interactions on mobile devices</li>
                    <li>Verify the interface adapts properly to different screen sizes</li>
                </ul>
            </div>
            
            <div class="responsive-test">
                <div class="device-frame">
                    <h4>📱 Mobile View</h4>
                    <div style="max-width: 320px; margin: 0 auto;">
                        <div class="la-addons-dragdrop-upload" 
                             data-form-id="123" 
                             data-field-name="mobile-test"
                             data-max-files="2"
                             data-max-size="5MB"
                             data-accepted-types="image/*">
                            
                            <div class="la-dragdrop-zone">
                                <div class="la-dragdrop-content">
                                    <div class="la-dragdrop-icon">
                                        <svg width="36" height="36" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                            <polyline points="7,10 12,15 17,10"></polyline>
                                            <line x1="12" y1="15" x2="12" y2="3"></line>
                                        </svg>
                                    </div>
                                    <div class="la-dragdrop-text">
                                        <p class="la-dragdrop-primary">Tap to select files</p>
                                        <p class="la-dragdrop-secondary">Max 2 files, 5MB each</p>
                                    </div>
                                </div>
                                <input type="file" name="mobile-test[]" multiple accept="image/*" style="display: none;">
                            </div>
                            <div class="la-dragdrop-files"></div>
                        </div>
                    </div>
                </div>
                
                <div class="device-frame">
                    <h4>💻 Desktop View</h4>
                    <div class="la-addons-dragdrop-upload" 
                         data-form-id="123" 
                         data-field-name="desktop-test"
                         data-max-files="5"
                         data-max-size="10MB"
                         data-accepted-types="*">
                        
                        <div class="la-dragdrop-zone">
                            <div class="la-dragdrop-content">
                                <div class="la-dragdrop-icon">
                                    <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                        <polyline points="7,10 12,15 17,10"></polyline>
                                        <line x1="12" y1="15" x2="12" y2="3"></line>
                                    </svg>
                                </div>
                                <div class="la-dragdrop-text">
                                    <p class="la-dragdrop-primary">Drop files here or <button type="button" class="la-dragdrop-browse">browse</button></p>
                                    <p class="la-dragdrop-secondary">Any file type, maximum 5 files, 10MB each</p>
                                </div>
                            </div>
                            <input type="file" name="desktop-test[]" multiple style="display: none;">
                        </div>
                        <div class="la-dragdrop-files"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Mock AJAX data for testing -->
    <script>
        // Mock WordPress AJAX data
        window.laAddonsDragDrop = {
            ajaxUrl: '/wp-admin/admin-ajax.php',
            nonce: 'test-nonce-123',
            maxFileSize: 10485760, // 10MB
            allowedTypes: {}
        };
    </script>
    
    <script src="../assets/js/drag-drop-upload.js"></script>
</body>
</html>
