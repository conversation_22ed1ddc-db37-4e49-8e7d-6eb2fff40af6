<?php
/**
 * Manual CF7 Test - Add this to your theme's functions.php
 * This will help us determine exactly what's wrong with CF7 tag registration
 */

// Test 1: Check if wpcf7_init hook fires at all
add_action('wpcf7_init', function() {
    error_log('MANUAL TEST: wpcf7_init hook fired!');
});

// Test 2: Check if we can register on plugins_loaded
add_action('plugins_loaded', function() {
    error_log('MANUAL TEST: plugins_loaded hook fired');
    
    if (function_exists('wpcf7_add_form_tag')) {
        error_log('MANUAL TEST: wpcf7_add_form_tag exists on plugins_loaded');
        
        wpcf7_add_form_tag('manual-test', function($tag) {
            error_log('MANUAL TEST: manual-test handler called');
            return '<div style="background: orange; padding: 20px; color: white; font-weight: bold;">
                🎉 MANUAL TEST WORKING!<br>
                This proves CF7 custom tags can work!
            </div>';
        });
        
        error_log('MANUAL TEST: manual-test tag registered on plugins_loaded');
    } else {
        error_log('MANUAL TEST: wpcf7_add_form_tag NOT available on plugins_loaded');
    }
}, 999);

// Test 3: Very late registration
add_action('wp', function() {
    error_log('MANUAL TEST: wp hook fired');
    
    if (function_exists('wpcf7_add_form_tag')) {
        error_log('MANUAL TEST: wpcf7_add_form_tag exists on wp hook');
        
        wpcf7_add_form_tag('late-test', function($tag) {
            error_log('MANUAL TEST: late-test handler called');
            return '<div style="background: red; padding: 20px; color: white; font-weight: bold;">
                🚀 LATE TEST WORKING!<br>
                This was registered on wp hook!
            </div>';
        });
        
        error_log('MANUAL TEST: late-test tag registered on wp hook');
    } else {
        error_log('MANUAL TEST: wpcf7_add_form_tag NOT available on wp hook');
    }
}, 999);

// Test 4: Check CF7 version and status
add_action('init', function() {
    error_log('MANUAL TEST: init hook fired');
    
    if (defined('WPCF7_VERSION')) {
        error_log('MANUAL TEST: CF7 Version: ' . WPCF7_VERSION);
    }
    
    if (class_exists('WPCF7_ContactForm')) {
        error_log('MANUAL TEST: WPCF7_ContactForm class exists');
    }
    
    // Check if CF7 is actually processing forms
    global $wp_filter;
    if (isset($wp_filter['wpcf7_init'])) {
        error_log('MANUAL TEST: wpcf7_init hook has ' . count($wp_filter['wpcf7_init']) . ' callbacks');
    } else {
        error_log('MANUAL TEST: wpcf7_init hook not found in wp_filter');
    }
});

// Test 5: Try the old deprecated method to see if it works
add_action('init', function() {
    if (function_exists('wpcf7_add_shortcode')) {
        error_log('MANUAL TEST: Using deprecated wpcf7_add_shortcode method');
        
        wpcf7_add_shortcode('deprecated-test', function($tag) {
            error_log('MANUAL TEST: deprecated-test handler called');
            return '<div style="background: purple; padding: 20px; color: white; font-weight: bold;">
                ⚠️ DEPRECATED METHOD WORKING!<br>
                This uses the old wpcf7_add_shortcode function!
            </div>';
        });
        
        error_log('MANUAL TEST: deprecated-test registered with old method');
    }
}, 999);
