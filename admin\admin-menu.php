<?php

function la_addons_admin_menu() {
    add_menu_page(
        __( 'Orbit Addons', 'la-addons' ),
        __( 'Orbit Addons', 'la-addons' ),
        'manage_options',
        'la-addons',
        'la_addons_submissions_page',
        'dashicons-database',
        30
    );
}

add_action( 'admin_menu', 'la_addons_admin_menu' );

function la_addons_submissions_page() {
    global $wpdb;
    $table_name = $wpdb->prefix . 'la_addons_submissions';

    // Search and filter
    $search = isset( $_GET['s'] ) ? sanitize_text_field( $_GET['s'] ) : '';
    $form_filter = isset( $_GET['form'] ) ? (int) $_GET['form'] : 0;
    $date_filter = isset( $_GET['date'] ) ? sanitize_text_field( $_GET['date'] ) : '';

    $where = '';
    if ( ! empty( $search ) ) {
        $where .= $wpdb->prepare( " AND form_data LIKE %s", '%' . $wpdb->esc_like( $search ) . '%' );
    }
    if ( ! empty( $form_filter ) ) {
        $where .= $wpdb->prepare( " AND form_id = %d", $form_filter );
    }
    if ( ! empty( $date_filter ) ) {
        $where .= $wpdb->prepare( " AND DATE(submission_time) = %s", $date_filter );
    }

    // Pagination
    $per_page = 20;
    $current_page = isset( $_GET['paged'] ) ? (int) $_GET['paged'] : 1;
    $total_items = $wpdb->get_var( "SELECT COUNT(id) FROM $table_name WHERE 1=1 $where" );
    $total_pages = ceil( $total_items / $per_page );
    $offset = ( $current_page - 1 ) * $per_page;

    $submissions = $wpdb->get_results( "SELECT * FROM $table_name WHERE 1=1 $where ORDER BY submission_time DESC LIMIT $per_page OFFSET $offset" );

    // Get all unique field names across all submissions for table headers
    $all_field_names = array();
    foreach ( $submissions as $submission ) {
        $form_data = maybe_unserialize( $submission->form_data );
        if ( is_array( $form_data ) ) {
            foreach ( array_keys( $form_data ) as $field_name ) {
                // Skip internal CF7 fields
                if ( strpos( $field_name, '_wpcf7' ) === 0 || $field_name === '_wpcf7_version' || $field_name === '_wpcf7_locale' || 
                     $field_name === '_wpcf7_unit_tag' || $field_name === '_wpcf7_container_post' ) {
                    continue;
                }
                $all_field_names[$field_name] = $field_name;
            }
        }
    }
    
    // Sort field names alphabetically
    ksort($all_field_names);

    ?>
    <div class="wrap">
        <h1><?php echo esc_html__( 'Contact Form Submissions', 'la-addons' ); ?></h1>

        <form method="get">
            <input type="hidden" name="page" value="la-addons">
            <?php
            // Search box
            echo '<input type="search" name="s" value="' . esc_attr( $search ) . '" placeholder="' . esc_attr__( 'Search submissions...', 'la-addons' ) . '" />';
            // Form filter
            $forms = get_posts( array( 'post_type' => 'wpcf7_contact_form', 'posts_per_page' => -1 ) );
            echo '<select name="form">';
            echo '<option value="0">' . esc_html__( 'All Forms', 'la-addons' ) . '</option>';
            foreach ( $forms as $form ) {
                echo '<option value="' . esc_attr( $form->ID ) . '" ' . selected( $form_filter, $form->ID, false ) . '>' . esc_html( $form->post_title ) . '</option>';
            }
            echo '</select>';
            // Date filter
            echo '<input type="date" name="date" value="' . esc_attr( $date_filter ) . '" />';
            submit_button( __( 'Filter', 'la-addons' ), 'button', '', false );
            ?>
        </form>

        <form method="post">
            <input type="hidden" name="page" value="la-addons">
            <input type="hidden" name="action" value="export_csv">
            <?php submit_button( __( 'Export to CSV', 'la-addons' ), 'primary', 'export_csv' ); ?>
        </form>

        <table class="wp-list-table widefat fixed striped">
            <thead>
                <tr>
                    <th scope="col" class="manage-column column-primary"><?php echo esc_html__( 'ID', 'la-addons' ); ?></th>
                    <th scope="col" class="manage-column"><?php echo esc_html__( 'Form', 'la-addons' ); ?></th>
                    <th scope="col" class="manage-column"><?php echo esc_html__( 'Submission Time', 'la-addons' ); ?></th>
                    <th scope="col" class="manage-column"><?php echo esc_html__( 'Status', 'la-addons' ); ?></th>
                    <th scope="col" class="manage-column"><?php echo esc_html__( 'Details', 'la-addons' ); ?></th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ( $submissions as $submission ) : 
                    $form_data = maybe_unserialize( $submission->form_data );
                    $form_title = get_the_title( $submission->form_id );
                ?>
                    <tr>
                        <td><?php echo esc_html( $submission->id ); ?></td>
                        <td><?php echo esc_html( $form_title ); ?></td>
                        <td><?php echo esc_html( $submission->submission_time ); ?></td>
                        <td><?php echo esc_html( $submission->status ); ?></td>
                        <td>
                            <button type="button" class="button toggle-details" data-submission-id="<?php echo esc_attr( $submission->id ); ?>">
                                <?php echo esc_html__( 'View Details', 'la-addons' ); ?>
                            </button>
                        </td>
                    </tr>
                    <tr class="submission-details" id="submission-<?php echo esc_attr( $submission->id ); ?>" style="display: none;">
                        <td colspan="5">
                            <table class="widefat fixed">
                                <thead>
                                    <tr>
                                        <th><?php echo esc_html__( 'Field', 'la-addons' ); ?></th>
                                        <th><?php echo esc_html__( 'Value', 'la-addons' ); ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php 
                                    if ( is_array( $form_data ) ) :
                                        foreach ( $form_data as $field_name => $field_value ) :
                                            // Skip internal CF7 fields
                                            if ( strpos( $field_name, '_wpcf7' ) === 0 || $field_name === '_wpcf7_version' || $field_name === '_wpcf7_locale' || 
                                                 $field_name === '_wpcf7_unit_tag' || $field_name === '_wpcf7_container_post' ) {
                                                continue;
                                            }
                                            
                                            // Handle arrays (like checkboxes)
                                            if ( is_array( $field_value ) ) {
                                                $field_value = implode( ', ', $field_value );
                                            }
                                            
                                            // Handle signature or file uploads (URLs)
                                            if ( filter_var( $field_value, FILTER_VALIDATE_URL ) ) {
                                                if ( strpos( $field_value, 'signature' ) !== false ) {
                                                    $field_value = '<img src="' . esc_url( $field_value ) . '" alt="Signature" style="max-width: 200px; max-height: 100px;" />';
                                                } else {
                                                    $field_value = '<a href="' . esc_url( $field_value ) . '" target="_blank">' . esc_html__( 'View File', 'la-addons' ) . '</a>';
                                                }
                                            }
                                    ?>
                                            <tr>
                                                <td><?php echo esc_html( $field_name ); ?></td>
                                                <td><?php echo wp_kses_post( $field_value ); ?></td>
                                            </tr>
                                    <?php 
                                        endforeach;
                                    endif;
                                    ?>
                                </tbody>
                            </table>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>

        <div class="tablenav">
            <div class="tablenav-pages">
                <?php
                echo paginate_links( array(
                    'base' => add_query_arg( 'paged', '%#%' ),
                    'format' => '',
                    'prev_text' => __( '&laquo;' ),
                    'next_text' => __( '&raquo;' ),
                    'total' => $total_pages,
                    'current' => $current_page
                ) );
                ?>
            </div>
        </div>
    </div>

    <script>
    jQuery(document).ready(function($) {
        $('.toggle-details').on('click', function() {
            var submissionId = $(this).data('submission-id');
            $('#submission-' + submissionId).toggle();
            
            if ($(this).text() === '<?php echo esc_js( __( 'View Details', 'la-addons' ) ); ?>') {
                $(this).text('<?php echo esc_js( __( 'Hide Details', 'la-addons' ) ); ?>');
            } else {
                $(this).text('<?php echo esc_js( __( 'View Details', 'la-addons' ) ); ?>');
            }
        });
    });
    </script>
    <?php
}

function la_addons_export_csv() {
    if ( isset( $_POST['export_csv'] ) ) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'la_addons_submissions';
        
        // Apply filters if set
        $where = '';
        if ( isset( $_GET['s'] ) && !empty( $_GET['s'] ) ) {
            $search = sanitize_text_field( $_GET['s'] );
            $where .= $wpdb->prepare( " AND form_data LIKE %s", '%' . $wpdb->esc_like( $search ) . '%' );
        }
        if ( isset( $_GET['form'] ) && !empty( $_GET['form'] ) ) {
            $form_filter = (int) $_GET['form'];
            $where .= $wpdb->prepare( " AND form_id = %d", $form_filter );
        }
        if ( isset( $_GET['date'] ) && !empty( $_GET['date'] ) ) {
            $date_filter = sanitize_text_field( $_GET['date'] );
            $where .= $wpdb->prepare( " AND DATE(submission_time) = %s", $date_filter );
        }
        
        $submissions = $wpdb->get_results( "SELECT * FROM $table_name WHERE 1=1 $where ORDER BY submission_time DESC" );
        
        // Get all unique field names across all submissions for CSV headers
        $all_field_names = array();
        foreach ( $submissions as $submission ) {
            $form_data = maybe_unserialize( $submission->form_data );
            if ( is_array( $form_data ) ) {
                foreach ( array_keys( $form_data ) as $field_name ) {
                    // Skip internal CF7 fields
                    if ( strpos( $field_name, '_wpcf7' ) === 0 || $field_name === '_wpcf7_version' || $field_name === '_wpcf7_locale' || 
                         $field_name === '_wpcf7_unit_tag' || $field_name === '_wpcf7_container_post' ) {
                        continue;
                    }
                    $all_field_names[$field_name] = $field_name;
                }
            }
        }
        
        // Sort field names alphabetically
        ksort($all_field_names);
        
        // Prepare CSV headers
        $csv_headers = array(
            'ID',
            'Form ID',
            'Form Name',
            'Submission Time',
            'Status'
        );
        
        // Add field names to headers
        $csv_headers = array_merge($csv_headers, array_values($all_field_names));

        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment; filename=submissions-' . date('Y-m-d') . '.csv');
        $output = fopen('php://output', 'w');
        
        // Write headers
        fputcsv($output, $csv_headers);

        // Write data rows
        foreach ($submissions as $submission) {
            $form_data = maybe_unserialize($submission->form_data);
            $form_title = get_the_title($submission->form_id);
            
            // Start with the standard columns
            $row = array(
                $submission->id,
                $submission->form_id,
                $form_title,
                $submission->submission_time,
                $submission->status
            );
            
            // Add field values in the same order as headers
            foreach ($all_field_names as $field_name) {
                if (isset($form_data[$field_name])) {
                    $field_value = $form_data[$field_name];
                    
                    // Handle arrays (like checkboxes)
                    if (is_array($field_value)) {
                        $field_value = implode(', ', $field_value);
                    }
                    
                    // For URLs (like signatures or file uploads), just include the URL
                    $row[] = $field_value;
                } else {
                    $row[] = ''; // Empty value if field doesn't exist in this submission
                }
            }
            
            fputcsv($output, $row);
        }

        fclose($output);
        exit;
    }
}

add_action( 'admin_init', 'la_addons_export_csv' );


