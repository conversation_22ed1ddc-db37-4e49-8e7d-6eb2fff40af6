# 📁 Drag & Drop Multiple File Upload Documentation

## Overview

The Drag & Drop Multiple File Upload feature provides a modern, user-friendly way to upload multiple files to Contact Form 7 forms. It includes drag-and-drop functionality, file previews, progress indicators, and comprehensive validation.

## ✨ Features

### Core Functionality
- **Drag & Drop Interface**: Intuitive drag-and-drop zone with visual feedback
- **Multiple File Selection**: Support for selecting and uploading multiple files at once
- **File Previews**: Thumbnail previews for images and file type icons for documents
- **Progress Indicators**: Real-time upload progress with individual file status
- **File Management**: Add/remove files before upload with file information display

### Validation & Security
- **File Type Validation**: Configurable file type restrictions (MIME types and extensions)
- **File Size Limits**: Per-file and total size limits with server-side validation
- **Security Scanning**: Malware detection and suspicious content filtering
- **Filename Validation**: Protection against directory traversal and malicious filenames

### User Experience
- **Responsive Design**: Mobile-friendly interface that works on all devices
- **Accessibility**: Keyboard navigation and screen reader support
- **Error Handling**: Clear error messages and validation feedback
- **WordPress Integration**: Seamless integration with WordPress Media Library

## 🚀 Quick Start

### 1. Basic Usage

Add a drag-and-drop upload field to your Contact Form 7 form:

```
[dragdrop-upload file-upload]
```

### 2. With Configuration Options

```
[dragdrop-upload documents max-files:5 max-size:10MB accept:"application/pdf,.doc,.docx"]
```

### 3. Complete Form Example

```html
<p>Your Name (required)<br>
[text* your-name]</p>

<p>Your Email (required)<br>
[email* your-email]</p>

<p>Upload Documents<br>
[dragdrop-upload documents max-files:3 max-size:5MB accept:"application/pdf,.doc,.docx"]</p>

<p>Upload Images<br>
[dragdrop-upload photos max-files:10 max-size:2MB accept:"image/*"]</p>

<p>[submit "Send"]</p>
```

## ⚙️ Configuration Options

### Form Tag Attributes

| Attribute | Description | Default | Example |
|-----------|-------------|---------|---------|
| `max-files` | Maximum number of files | 5 | `max-files:10` |
| `max-size` | Maximum file size per file | 10MB | `max-size:5MB` |
| `accept` | Allowed file types | `image/*,application/pdf,.doc,.docx` | `accept:"image/*"` |
| `id` | HTML ID attribute | Auto-generated | `id:my-upload` |
| `class` | CSS class attribute | Default classes | `class:custom-upload` |

### File Type Examples

```
# Images only
accept:"image/*"

# Specific image types
accept:".jpg,.jpeg,.png,.gif"

# Documents
accept:"application/pdf,.doc,.docx,.txt"

# Mixed types
accept:"image/*,application/pdf,.doc,.docx,.txt"

# Archives
accept:".zip,.rar,.7z"

# All files (not recommended)
accept:"*"
```

### Size Format Examples

```
max-size:1MB      # 1 Megabyte
max-size:500KB    # 500 Kilobytes
max-size:2GB      # 2 Gigabytes
max-size:1048576  # 1MB in bytes
```

## 🎛️ Admin Configuration

### Global Settings

Navigate to **Contact Form 7 → Edit Form → Drag & Drop Upload** tab to configure:

#### File Limits
- **Maximum Files**: Set the default maximum number of files (1-50)
- **Maximum File Size**: Set the default file size limit
- **Allowed File Types**: Configure default allowed file types

#### Upload Behavior
- **Auto-upload files when selected**: Files upload immediately vs. on form submission
- **Show file previews/thumbnails**: Enable/disable image thumbnails
- **Show upload progress indicators**: Enable/disable progress bars

#### File Management
- **Duplicate Files**: How to handle files with the same name
  - Rename automatically (default)
  - Replace existing file
  - Skip duplicate files

- **File Naming**: Control how uploaded files are named
  - Keep original names
  - Add timestamp
  - Custom pattern: `{name}-{timestamp}-{random}`

#### Security
- **Enable security scanning**: Scan files for malicious content
- **File type validation**: Strict MIME type checking

### Custom Naming Patterns

Available placeholders for custom file naming:

| Placeholder | Description | Example |
|-------------|-------------|---------|
| `{name}` | Original filename (without extension) | `document` |
| `{timestamp}` | Unix timestamp | `1640995200` |
| `{random}` | Random 8-character string | `a7b9c2d4` |
| `{form_id}` | Contact Form 7 form ID | `123` |
| `{field_name}` | Upload field name | `documents` |

Example patterns:
```
{name}-{timestamp}           # document-1640995200.pdf
{form_id}-{field_name}-{name} # 123-documents-document.pdf
upload-{random}              # upload-a7b9c2d4.pdf
```

## 🎨 Styling & Customization

### CSS Classes

The drag-and-drop upload component uses these CSS classes:

```css
.la-addons-dragdrop-upload     /* Main container */
.la-dragdrop-zone              /* Drop zone area */
.la-dragdrop-dragover          /* Active drag state */
.la-dragdrop-disabled          /* Disabled state */
.la-dragdrop-file-list         /* File list container */
.la-file-item                  /* Individual file item */
.la-file-preview               /* File preview/icon area */
.la-file-thumbnail             /* Image thumbnail */
.la-file-info                  /* File information */
.la-file-name                  /* File name */
.la-file-details               /* File size and type */
.la-file-actions               /* Action buttons */
.la-file-progress              /* Progress bar */
.la-status-indicator           /* Status icons */
```

### Custom Styling Example

```css
/* Custom drop zone styling */
.la-dragdrop-zone {
    border: 3px dashed #007cba;
    background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%);
    border-radius: 12px;
}

.la-dragdrop-zone:hover {
    border-color: #005a87;
    transform: scale(1.01);
}

/* Custom file item styling */
.la-file-item {
    background: #ffffff;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Custom progress bar */
.la-progress-fill {
    background: linear-gradient(90deg, #007cba 0%, #00a0d2 100%);
}
```

## 🔧 JavaScript API

### Initialization

```javascript
// Initialize drag-drop upload on custom elements
const uploadComponent = new DragDropUpload(containerElement);
```

### Events

```javascript
// Listen for file upload events
document.addEventListener('la-dragdrop-file-added', function(event) {
    console.log('File added:', event.detail.file);
});

document.addEventListener('la-dragdrop-file-uploaded', function(event) {
    console.log('File uploaded:', event.detail.file);
});

document.addEventListener('la-dragdrop-upload-complete', function(event) {
    console.log('All files uploaded:', event.detail.files);
});
```

### Methods

```javascript
const uploader = new DragDropUpload(element);

// Add files programmatically
uploader.addFiles(fileList);

// Remove a file
uploader.removeFile(fileId);

// Start upload
uploader.uploadFiles();

// Get uploaded files
const uploadedFiles = uploader.getUploadedFiles();
```

## 🔗 Integration with Other Features

### Conditional Fields

Drag-and-drop upload fields work seamlessly with conditional logic:

```html
<!-- Show upload field only when "Business" is selected -->
[radio application-type "Personal" "Business"]

<!-- This field will be hidden/shown based on the radio selection -->
[dragdrop-upload business-docs max-files:5 accept:"application/pdf,.doc,.docx"]
```

### Digital Signatures

Combine with signature fields for complete document workflows:

```html
[dragdrop-upload documents]
[signature user-signature]
```

## 📱 Mobile Considerations

### Touch Interactions
- Tap to open file browser on mobile devices
- Optimized touch targets for buttons and controls
- Responsive layout adapts to screen size

### Performance
- Automatic image resizing for mobile uploads
- Progressive loading of file previews
- Optimized for slower mobile connections

## 🛠️ Troubleshooting

### Common Issues

**Files not uploading:**
- Check server upload limits (`upload_max_filesize`, `post_max_size`)
- Verify file permissions on uploads directory
- Check WordPress allowed file types

**Drag and drop not working:**
- Ensure JavaScript is enabled
- Check for JavaScript errors in browser console
- Verify AJAX endpoints are accessible

**File validation errors:**
- Check file type configuration
- Verify file size limits
- Review security scanning settings

### Debug Mode

Enable debug logging by adding to `wp-config.php`:

```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
```

Check `/wp-content/debug.log` for detailed error messages.

## 🔒 Security Best Practices

1. **File Type Restrictions**: Always limit allowed file types
2. **Size Limits**: Set reasonable file size limits
3. **Security Scanning**: Keep security scanning enabled
4. **Regular Updates**: Keep the plugin updated
5. **Server Configuration**: Configure server-level upload restrictions

## 📊 Performance Optimization

1. **File Size Limits**: Set appropriate limits to prevent server overload
2. **Concurrent Uploads**: Files upload sequentially to prevent server strain
3. **Progress Feedback**: Real-time progress keeps users informed
4. **Error Recovery**: Automatic retry for failed uploads

## 🆘 Support

For support and bug reports:
1. Check the troubleshooting section above
2. Review server error logs
3. Test with default WordPress theme
4. Disable other plugins to check for conflicts

## 📝 Changelog

### Version 1.0.0
- Initial release with drag-and-drop functionality
- Multiple file upload support
- File previews and thumbnails
- Progress indicators
- Comprehensive validation and security
- WordPress Media Library integration
- Responsive design
- Conditional fields integration
