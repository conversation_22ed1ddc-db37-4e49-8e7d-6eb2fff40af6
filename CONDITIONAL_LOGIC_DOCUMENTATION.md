# Conditional Logic for Contact Form 7 - Complete Documentation

## Table of Contents

1. [Overview](#overview)
2. [Features](#features)
3. [Installation & Setup](#installation--setup)
4. [User Interface](#user-interface)
5. [Supported Field Types](#supported-field-types)
6. [Operators](#operators)
7. [Logic Types](#logic-types)
8. [Technical Architecture](#technical-architecture)
9. [API Reference](#api-reference)
10. [Frontend Implementation](#frontend-implementation)
11. [Troubleshooting](#troubleshooting)
12. [Browser Compatibility](#browser-compatibility)

## Overview

The Conditional Logic functionality for Contact Form 7 allows you to show or hide form fields based on user input. This creates dynamic, interactive forms that adapt to user responses in real-time.

### Key Benefits

- **Dynamic Forms**: Fields appear/disappear based on user input
- **Better UX**: Reduces form complexity by showing only relevant fields
- **Multiple Conditions**: Support for complex logic with AND/OR operations
- **Real-time Updates**: Instant field visibility changes without page reload
- **Extensive Operators**: 10 different comparison operators supported

## Features

### Core Functionality

- ✅ **Show/Hide Fields**: Control field visibility based on conditions
- ✅ **Multiple Rule Groups**: Create multiple independent rule sets
- ✅ **AND/OR Logic**: Combine conditions with logical operators
- ✅ **Real-time Evaluation**: Instant updates as users type/select
- ✅ **Nested Conditions**: Multiple conditions per rule group
- ✅ **Field Type Support**: Works with all CF7 field types

### Advanced Features

- ✅ **Dynamic Value Lists**: Auto-complete for select/radio field values
- ✅ **Template System**: Reusable rule and condition templates
- ✅ **Debug Mode**: Console logging for troubleshooting
- ✅ **API Integration**: REST API and AJAX endpoints
- ✅ **Fallback Methods**: Multiple data retrieval methods for reliability

## Installation & Setup

### Prerequisites

- WordPress 5.0+
- Contact Form 7 plugin installed and activated
- LA Addons plugin installed

### Activation

1. Install and activate the LA Addons plugin
2. The Conditional Logic tab will automatically appear in CF7 form editors
3. No additional configuration required

## User Interface

### Accessing Conditional Logic

1. Go to **Contact > Contact Forms** in WordPress admin
2. Edit any contact form
3. Click the **"Conditional Logic"** tab
4. Start creating rules with **"Add Rule Group"**

### Rule Group Structure

```
Rule Group #1
├── Target Field: [Select field to show/hide]
├── Action: [Show/Hide]
├── Logic: [All conditions match (AND) / Any condition matches (OR)]
└── Conditions:
    ├── Condition 1: [Field] [Operator] [Value]
    ├── Condition 2: [Field] [Operator] [Value]
    └── [Add Condition button]
```

### UI Components

- **Rule Group Header**: Shows group number and delete button
- **Target Field Dropdown**: Select which field to control
- **Action Dropdown**: Choose "Show" or "Hide"
- **Logic Dropdown**: Select AND or OR for multiple conditions
- **Condition Rows**: Individual field comparisons
- **Add/Remove Buttons**: Manage conditions and rule groups

## Supported Field Types

### Input Fields

- **Text**: `[text field-name]`
- **Email**: `[email email-field]`
- **URL**: `[url website]`
- **Tel**: `[tel phone]`
- **Number**: `[number quantity]`
- **Date**: `[date event-date]`
- **Textarea**: `[textarea message]`

### Selection Fields

- **Dropdown**: `[select menu-1 "Option 1" "Option 2"]`
- **Radio Buttons**: `[radio radio-1 "Yes" "No"]`
- **Checkboxes**: `[checkbox checkbox-1 "Option 1" "Option 2"]`

### Special Fields

- **File Upload**: `[file upload-field]`
- **Acceptance**: `[acceptance acceptance-1]`

### Field Detection

The system automatically detects all fields in your form and populates the dropdown menus. Field names are extracted from CF7 shortcodes.

## Operators

### Text Comparison

- **equals**: Exact match (case-sensitive)
- **does not equal**: Not equal to value
- **contains**: Value contains substring
- **does not contain**: Value doesn't contain substring
- **starts with**: Value begins with string
- **ends with**: Value ends with string

### Numeric Comparison

- **is greater than**: Numeric comparison (>)
- **is less than**: Numeric comparison (<)

### Empty/Filled Check

- **is empty**: Field has no value
- **is not empty**: Field has any value

### Operator Behavior

- Text operators work with all field types
- Numeric operators convert values to numbers
- Empty checks work with all field types
- Case-sensitive comparisons for text operators

## Logic Types

### AND Logic (All conditions match)

```javascript
// All conditions must be true
condition1 AND condition2 AND condition3 = true
```

**Use Case**: Show field only when multiple specific conditions are met

### OR Logic (Any condition matches)

```javascript
// At least one condition must be true
condition1 OR condition2 OR condition3 = true
```

**Use Case**: Show field when any of several conditions are met

### Example Scenarios

#### AND Logic Example

```
Show "Company Details" field if:
- User Type equals "Business" AND
- Country equals "USA" AND
- Annual Revenue is greater than "100000"
```

#### OR Logic Example

```
Show "Special Discount" field if:
- User Type equals "Student" OR
- Age is less than "25" OR
- Membership equals "Premium"
```

## Technical Architecture

### File Structure

```
la-addons/
├── admin/
│   └── conditional-fields-ui.php      # Admin interface
├── assets/
│   ├── css/
│   │   └── admin-style.css            # Styling
│   └── js/
│       └── conditional-fields.js      # Frontend/Admin JS
├── includes/
│   └── conditional-fields-api.php     # API endpoints
└── tests/
    └── conditional-fields-test.html   # Test file
```

### Data Storage

- **Location**: WordPress post meta
- **Meta Key**: `_la_addons_conditional_logic`
- **Format**: Serialized PHP array
- **Structure**:

```php
array(
    0 => array(
        'target_field' => 'field-name',
        'action' => 'show', // or 'hide'
        'logic' => 'all',   // or 'any'
        'conditions' => array(
            0 => array(
                'field' => 'trigger-field',
                'operator' => 'equal',
                'value' => 'trigger-value'
            )
        )
    )
)
```

### Processing Flow

1. **Admin Save**: Rules saved to post meta via CF7 save hooks
2. **Frontend Load**: JavaScript fetches rules via API/AJAX
3. **Event Binding**: Input events trigger rule evaluation
4. **Rule Evaluation**: Conditions checked against current values
5. **DOM Update**: Fields shown/hidden with CSS classes

## API Reference

### REST API Endpoints

#### Get Conditional Logic

```
GET /wp-json/la-addons/v1/forms/{id}/conditional-logic
```

**Parameters:**

- `id` (integer): Contact Form 7 form ID

**Response:**

```json
{
  "success": true,
  "data": {
    "conditionalLogic": [
      {
        "target_field": "field-name",
        "action": "show",
        "logic": "all",
        "conditions": [
          {
            "field": "trigger-field",
            "operator": "equal",
            "value": "trigger-value"
          }
        ]
      }
    ]
  }
}
```

### AJAX Endpoints

#### Get Conditional Logic (AJAX)

```
POST /wp-admin/admin-ajax.php
```

**Parameters:**

- `action`: `la_addons_get_conditional_logic`
- `form_id` (integer): Contact Form 7 form ID
- `nonce` (string): Security nonce

**Response:**

```json
{
    "success": true,
    "data": {
        "conditionalLogic": [...]
    }
}
```

### JavaScript API

#### Global Objects

```javascript
// Configuration object (localized from PHP)
window.laAddonsConditionalFields = {
  ajaxUrl: "/wp-admin/admin-ajax.php",
  restUrl: "/wp-json/",
  nonce: "security-nonce",
  restNonce: "rest-nonce",
  i18n: {
    ruleGroup: "Rule Group",
    // ... other translations
  },
};
```

#### Core Functions

```javascript
// Initialize conditional logic for all forms
initializeConditionalLogic();

// Evaluate rules for a specific form
evaluateRules(form, rules);

// Check individual condition
evaluateCondition(form, condition);

// Show/hide field with animation
toggleFieldVisibility(form, fieldName, shouldShow, action);
```

## Frontend Implementation

### Initialization Process

1. **DOM Ready**: Script waits for DOM content to load
2. **Form Detection**: Finds all `.wpcf7-form` elements
3. **Data Retrieval**: Fetches conditional logic rules via API
4. **Event Binding**: Attaches listeners to form inputs
5. **Initial Evaluation**: Runs rules once to set initial state

### Event Handling

```javascript
// Events that trigger rule evaluation
const events = ["change", "input", "keyup"];

// Debounced evaluation (100ms delay)
input.addEventListener(
  event,
  debounce(() => evaluateRules(form, rules), 100)
);
```

### Field Detection Methods

The system uses multiple methods to find form fields:

1. **Direct Name Match**: `[name="field-name"]`
2. **Wrapper Detection**: Finds parent containers
3. **Label Association**: Uses `for` attribute matching
4. **CF7 Specific**: `.wpcf7-form-control-wrap[data-name="field-name"]`

### CSS Classes Applied

```css
/* Hidden fields */
.la-addons-hidden {
  display: none !important;
  visibility: hidden !important;
}

/* Visible fields */
.la-addons-visible {
  display: block !important;
  visibility: visible !important;
}

/* Smooth transitions */
.wpcf7-form p,
.wpcf7-form div {
  transition: opacity 0.3s ease-in-out;
}
```

### Debugging Features

Enable browser console to see:

```javascript
// Form initialization
"Found X CF7 forms to initialize";

// API responses
"AJAX conditional logic response: {...}";
"REST API conditional logic response: {...}";

// Rule evaluation
"Evaluating X rule groups";
"Condition result: field=value, expected=value, result=true";

// Field visibility changes
"Toggling field visibility: field-name -> show/hide";
```

## Troubleshooting

### Common Issues

#### 1. Rules Not Loading

**Symptoms**: No conditional logic behavior on frontend
**Causes**:

- API endpoint not accessible
- Nonce verification failing
- Form ID not detected correctly

**Solutions**:

```javascript
// Check browser console for errors
// Verify form has data-form-id attribute
// Test API endpoint manually
```

#### 2. Fields Not Hiding/Showing

**Symptoms**: Rules load but fields don't change visibility
**Causes**:

- Field name mismatch
- CSS conflicts
- JavaScript errors

**Solutions**:

```javascript
// Verify field names match exactly
// Check for CSS !important conflicts
// Test with browser dev tools
```

#### 3. Performance Issues

**Symptoms**: Slow response when typing
**Causes**:

- Too many event listeners
- Complex rule evaluation
- No debouncing

**Solutions**:

```javascript
// Reduce number of conditions
// Check debounce timing (default: 100ms)
// Optimize rule complexity
```

### Debug Mode

Enable detailed logging:

```javascript
// Add to browser console
localStorage.setItem("laAddonsDebug", "true");
// Reload page to see detailed logs
```

### Testing Tools

Use the included test file:

```
/tests/conditional-fields-test.html
```

This provides a standalone testing environment with:

- Mock CF7 form structure
- Sample conditional logic rules
- Console debugging output
- Visual feedback for rule evaluation

## Browser Compatibility

### Supported Browsers

- ✅ Chrome 60+
- ✅ Firefox 55+
- ✅ Safari 12+
- ✅ Edge 79+
- ✅ Opera 47+

### JavaScript Features Used

- ES6 Arrow Functions
- Fetch API
- Promise/async-await
- Array methods (forEach, some, every)
- Template literals
- Destructuring assignment

### Polyfills (if needed)

For older browser support, include:

```html
<!-- Fetch API polyfill -->
<script src="https://polyfill.io/v3/polyfill.min.js?features=fetch"></script>

<!-- Promise polyfill -->
<script src="https://polyfill.io/v3/polyfill.min.js?features=Promise"></script>
```

## Advanced Usage Examples

### Example 1: Multi-Step Form Logic

```php
// Rule Group 1: Show step 2 when step 1 is complete
target_field: 'step-2-container'
action: 'show'
logic: 'all'
conditions: [
    ['name', 'is_not_empty', ''],
    ['email', 'is_not_empty', ''],
    ['phone', 'is_not_empty', '']
]

// Rule Group 2: Show business fields for business users
target_field: 'business-details'
action: 'show'
logic: 'all'
conditions: [
    ['user-type', 'equal', 'business']
]
```

### Example 2: Dynamic Pricing Display

```php
// Show premium pricing for premium features
target_field: 'premium-pricing'
action: 'show'
logic: 'any'
conditions: [
    ['service-level', 'equal', 'premium'],
    ['features', 'contains', 'advanced'],
    ['budget', 'greater_than', '1000']
]
```

### Example 3: Conditional Required Fields

```php
// Show required company info for business accounts
target_field: 'company-required-fields'
action: 'show'
logic: 'all'
conditions: [
    ['account-type', 'equal', 'business'],
    ['country', 'equal', 'USA']
]
```

## Performance Optimization

### Best Practices

1. **Minimize Rule Complexity**: Keep conditions simple and focused
2. **Use Debouncing**: Default 100ms delay prevents excessive evaluation
3. **Optimize Field Detection**: Use specific field names when possible
4. **Cache DOM Queries**: System automatically caches field references
5. **Limit Rule Groups**: Avoid excessive rule groups per form

### Performance Metrics

- **Rule Evaluation**: ~1-5ms per rule group
- **DOM Updates**: ~2-10ms per field toggle
- **API Calls**: ~50-200ms (cached after first load)
- **Memory Usage**: ~1-5MB for complex forms

## Security Considerations

### Data Validation

- All user inputs are sanitized before comparison
- SQL injection protection via WordPress nonce system
- XSS prevention through proper escaping

### Access Control

- Admin interface requires `manage_options` capability
- API endpoints use WordPress nonce verification
- Frontend evaluation uses read-only data

### Best Practices

1. Always validate form data server-side
2. Use nonces for all AJAX requests
3. Sanitize field values before storage
4. Escape output in admin interface

## Customization & Extension

### Custom Operators

Add new comparison operators:

```javascript
// In conditional-fields.js, extend evaluateCondition function
case 'custom_operator':
    return customLogic(fieldValue, condition.value);
```

### Custom Field Types

Support additional field types:

```php
// In conditional-fields-ui.php, extend field detection
$custom_fields = apply_filters('la_addons_conditional_fields', $form_fields);
```

### Styling Customization

Override default styles:

```css
/* Custom hide/show animations */
.la-addons-hidden {
  opacity: 0;
  transform: translateY(-10px);
  transition: all 0.3s ease;
}

.la-addons-visible {
  opacity: 1;
  transform: translateY(0);
  transition: all 0.3s ease;
}
```

### Hooks & Filters

Available WordPress hooks:

```php
// Filter form fields before processing
apply_filters('la_addons_conditional_form_fields', $fields, $form_id);

// Filter conditional logic rules before save
apply_filters('la_addons_conditional_logic_rules', $rules, $form_id);

// Action after rules are saved
do_action('la_addons_conditional_logic_saved', $form_id, $rules);
```

## Changelog & Version History

### Version 1.0.0 (Current)

- ✅ Initial release with core functionality
- ✅ Support for all CF7 field types
- ✅ 10 comparison operators
- ✅ AND/OR logic support
- ✅ REST API and AJAX endpoints
- ✅ Real-time field evaluation
- ✅ Admin interface with drag-and-drop
- ✅ Comprehensive error handling
- ✅ Debug mode and logging
- ✅ Browser compatibility testing

### Planned Features (Future Versions)

- 🔄 Visual rule builder with flowchart
- 🔄 Import/export rule sets
- 🔄 Rule templates and presets
- 🔄 Advanced field animations
- 🔄 Conditional validation rules
- 🔄 Integration with popular form builders
- 🔄 Multi-language support improvements

## Support & Resources

### Documentation

- This comprehensive guide
- Inline code comments
- Test files and examples
- WordPress Codex integration

### Community Support

- WordPress.org plugin forums
- GitHub issues and discussions
- Stack Overflow tags: `contact-form-7`, `conditional-logic`

### Professional Support

- Priority email support
- Custom development services
- Training and consultation
- Enterprise licensing options

---

**Last Updated**: 2025-07-13
**Version**: 1.0.0
**Compatibility**: WordPress 5.0+, Contact Form 7 5.0+
