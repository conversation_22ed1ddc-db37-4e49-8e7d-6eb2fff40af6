<?php
/**
 * Drag and Drop Multiple File Upload for Contact Form 7
 * Description: Adds drag-and-drop multiple file upload functionality to Contact Form 7.
 * Version: 1.0
 * Author: <PERSON><PERSON>
 */

// Block direct access to the file
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}



/**
 * Defines the handler for the [dragdrop-upload] form tag.
 * This function generates the HTML for the drag-and-drop upload area on the front-end.
 */
function la_addons_dragdrop_upload_form_tag_handler( $tag ) {
    error_log( 'LA Addons: dragdrop-upload form tag handler called!' );

    // Simple test first
    if ( ! $tag ) {
        return '<p style="color: red;">No tag provided</p>';
    }

    // Convert to WPCF7_FormTag if needed
    if ( is_array( $tag ) ) {
        $tag = new WPCF7_FormTag( $tag );
    }

    // Get field name
    $field_name = '';
    if ( method_exists( $tag, 'get_name' ) ) {
        $field_name = $tag->get_name();
    } elseif ( isset( $tag->name ) ) {
        $field_name = $tag->name;
    }

    if ( empty( $field_name ) ) {
        return '<p style="color: red;">dragdrop-upload requires a name. Use: [dragdrop-upload field-name]</p>';
    }

    error_log( 'LA Addons: Processing field: ' . $field_name );

    $class = wpcf7_form_controls_class( $tag->type, 'la-addons-dragdrop-upload' );
    $atts = array();
    $atts['class'] = $tag->get_class_option( $class );
    $atts['id'] = $tag->get_id_option();

    // Get current form ID safely
    $current_form = wpcf7_get_current_contact_form();
    $form_id = $current_form ? $current_form->id() : 0;

    $atts['data-form-id'] = $form_id;
    $atts['data-field-name'] = $field_name;

    // Get configuration options
    $max_files = $tag->get_option( 'max-files', 'int', true ) ?: 5;
    $max_size = $tag->get_option( 'max-size', '', true ) ?: '10MB';
    $accepted_types = $tag->get_option( 'accept', '', true ) ?: 'image/*,application/pdf,.doc,.docx';

    $atts['data-max-files'] = $max_files;
    $atts['data-max-size'] = $max_size;
    $atts['data-accepted-types'] = $accepted_types;

    $atts = wpcf7_format_atts( $atts );

    // Generate unique IDs for this instance
    $upload_id = 'la-dragdrop-' . uniqid();
    $file_input_id = $upload_id . '-input';

    // The HTML structure for drag-and-drop upload
    $html = sprintf(
        '<div %1$s id="%2$s">
            <div class="la-dragdrop-zone" data-upload-id="%2$s">
                <div class="la-dragdrop-content">
                    <div class="la-dragdrop-icon">
                        <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                            <polyline points="7,10 12,15 17,10"></polyline>
                            <line x1="12" y1="15" x2="12" y2="3"></line>
                        </svg>
                    </div>
                    <div class="la-dragdrop-text">
                        <p class="la-dragdrop-primary">Drop files here or <button type="button" class="la-dragdrop-browse">browse</button></p>
                        <p class="la-dragdrop-secondary">Maximum %3$d files, %4$s each</p>
                    </div>
                </div>
                <input type="file" id="%5$s" name="%6$s[]" multiple accept="%7$s" style="display: none;" />
            </div>
            <div class="la-dragdrop-files" id="%2$s-files"></div>
            <div class="la-dragdrop-progress" id="%2$s-progress" style="display: none;">
                <div class="la-progress-bar">
                    <div class="la-progress-fill"></div>
                </div>
                <div class="la-progress-text">Uploading...</div>
            </div>
        </div>',
        $atts,
        $upload_id,
        $max_files,
        $max_size,
        $file_input_id,
        $field_name,
        $accepted_types
    );

    error_log( 'LA Addons: Generated HTML for dragdrop-upload field: ' . $field_name );

    return $html;

    return $html;
}

/**
 * Registers the new form tag with Contact Form 7.
 * This function is hooked to `wpcf7_init` to ensure it runs on both frontend and backend.
 */
function la_addons_register_dragdrop_upload_form_tag() {
    if ( function_exists( 'wpcf7_add_form_tag' ) ) {
        wpcf7_add_form_tag(
            'dragdrop-upload',
            'la_addons_dragdrop_upload_form_tag_handler',
            array(
                'name-attr' => true
            )
        );

        wpcf7_add_form_tag(
            'dragdrop-upload*',
            'la_addons_dragdrop_upload_form_tag_handler',
            array(
                'name-attr' => true
            )
        );

        error_log( 'LA Addons: Registered dragdrop-upload form tags' );
    } else {
        error_log( 'LA Addons: wpcf7_add_form_tag function not available' );
    }
}

// Try multiple hooks to ensure registration
add_action( 'wpcf7_init', 'la_addons_register_dragdrop_upload_form_tag', 10 );
add_action( 'init', 'la_addons_register_dragdrop_upload_form_tag', 20 );
add_action( 'plugins_loaded', 'la_addons_register_dragdrop_upload_form_tag', 30 );

// Admin notice to help with debugging
function la_addons_dragdrop_admin_notice() {
    if ( ! function_exists( 'wpcf7_add_form_tag' ) ) {
        echo '<div class="notice notice-error"><p><strong>LA Addons:</strong> Contact Form 7 is required for drag-and-drop upload functionality.</p></div>';
    }
}
add_action( 'admin_notices', 'la_addons_dragdrop_admin_notice' );

// Test function to verify tag registration
function la_addons_test_dragdrop_tag_registration() {
    // Add a simple test handler to see if CF7 is working at all
    if ( function_exists( 'wpcf7_add_form_tag' ) ) {
        wpcf7_add_form_tag( 'test-dragdrop', function() {
            error_log( 'LA Addons: test-dragdrop handler called!' );
            return '<p style="color: green;">TEST DRAGDROP WORKING!</p>';
        });
        error_log( 'LA Addons: Registered test-dragdrop tag for verification' );
    }
}
add_action( 'init', 'la_addons_test_dragdrop_tag_registration', 999 );

/**
 * Tag generator callback for the admin area.
 * This function generates the form for configuring the drag-and-drop upload field.
 */
function la_addons_dragdrop_upload_tag_generator_callback( $contact_form, $args = '' ) {
    $args = wp_parse_args( $args, array() );
    $description = __( 'Generate a form-tag for a drag-and-drop file upload field.', 'la-addons' );

    ?>
    <div class="control-box">
        <fieldset>
            <legend><?php echo esc_html( $description ); ?></legend>
            <table class="form-table">
                <tbody>
                    <tr>
                        <th scope="row">
                            <label for="<?php echo esc_attr( $args['content'] . '-name' ); ?>">
                                <?php echo esc_html( __( 'Name', 'la-addons' ) ); ?>
                            </label>
                        </th>
                        <td>
                            <input type="text" name="name" class="tg-name oneline" id="<?php echo esc_attr( $args['content'] . '-name' ); ?>" />
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">
                            <label for="<?php echo esc_attr( $args['content'] . '-max-files' ); ?>">
                                <?php echo esc_html( __( 'Maximum Files', 'la-addons' ) ); ?>
                            </label>
                        </th>
                        <td>
                            <input type="number" name="max-files" class="numeric oneline" min="1" max="20" value="5" id="<?php echo esc_attr( $args['content'] . '-max-files' ); ?>" />
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">
                            <label for="<?php echo esc_attr( $args['content'] . '-max-size' ); ?>">
                                <?php echo esc_html( __( 'Maximum File Size', 'la-addons' ) ); ?>
                            </label>
                        </th>
                        <td>
                            <input type="text" name="max-size" class="oneline" value="10MB" id="<?php echo esc_attr( $args['content'] . '-max-size' ); ?>" />
                            <p class="description"><?php echo esc_html( __( 'Examples: 10MB, 5MB, 2048KB', 'la-addons' ) ); ?></p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">
                            <label for="<?php echo esc_attr( $args['content'] . '-accept' ); ?>">
                                <?php echo esc_html( __( 'Accepted File Types', 'la-addons' ) ); ?>
                            </label>
                        </th>
                        <td>
                            <input type="text" name="accept" class="wide" value="image/*,application/pdf,.doc,.docx" id="<?php echo esc_attr( $args['content'] . '-accept' ); ?>" />
                            <p class="description"><?php echo esc_html( __( 'Examples: image/*, .pdf, .doc, .docx, .txt', 'la-addons' ) ); ?></p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">
                            <label for="<?php echo esc_attr( $args['content'] . '-id' ); ?>">
                                <?php echo esc_html( __( 'ID attribute', 'la-addons' ) ); ?>
                            </label>
                        </th>
                        <td>
                            <input type="text" name="id" class="idvalue oneline option" id="<?php echo esc_attr( $args['content'] . '-id' ); ?>" />
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">
                            <label for="<?php echo esc_attr( $args['content'] . '-class' ); ?>">
                                <?php echo esc_html( __( 'Class attribute', 'la-addons' ) ); ?>
                            </label>
                        </th>
                        <td>
                            <input type="text" name="class" class="classvalue oneline option" id="<?php echo esc_attr( $args['content'] . '-class' ); ?>" />
                        </td>
                    </tr>
                </tbody>
            </table>
        </fieldset>
    </div>

    <div class="insert-box">
        <input type="text" name="dragdrop-upload" class="tag code" readonly="readonly" onfocus="this.select()" />

        <div class="submitbox">
            <input type="button" class="button button-primary insert-tag" value="<?php echo esc_attr( __( 'Insert Tag', 'la-addons' ) ); ?>" />
        </div>
    </div>
    <?php
}

/**
 * Registers the tag generator with Contact Form 7 for the admin area.
 * This function is hooked to `wpcf7_admin_init`.
 */
function la_addons_add_dragdrop_upload_tag_generator() {
    if ( function_exists( 'wpcf7_add_tag_generator' ) ) {
        wpcf7_add_tag_generator(
            'dragdrop-upload',
            __( 'Drag & Drop Upload', 'la-addons' ),
            'la_addons_dragdrop_upload_tag_generator_callback',
            array( 'title' => __( 'Drag & Drop File Upload', 'la-addons' ) )
        );
    }
}
add_action( 'wpcf7_admin_init', 'la_addons_add_dragdrop_upload_tag_generator', 20, 0 );

/**
 * AJAX handler for file upload processing
 */
function la_addons_handle_dragdrop_upload() {
    // Verify nonce
    if ( ! wp_verify_nonce( $_POST['nonce'], 'la_addons_dragdrop_upload' ) ) {
        wp_die( 'Security check failed' );
    }

    $response = array( 'success' => false, 'files' => array(), 'errors' => array() );
    $form_id = isset( $_POST['form_id'] ) ? intval( $_POST['form_id'] ) : 0;
    $field_name = isset( $_POST['field_name'] ) ? sanitize_text_field( $_POST['field_name'] ) : '';

    // Get form-specific options
    $form_options = array();
    if ( $form_id ) {
        $form_options = la_addons_get_dragdrop_upload_options( $form_id );
    }

    if ( ! empty( $_FILES['files'] ) ) {
        $files = $_FILES['files'];
        $file_count = count( $files['name'] );

        for ( $i = 0; $i < $file_count; $i++ ) {
            if ( $files['error'][$i] === UPLOAD_ERR_OK ) {
                $file_data = array(
                    'name' => $files['name'][$i],
                    'type' => $files['type'][$i],
                    'tmp_name' => $files['tmp_name'][$i],
                    'error' => $files['error'][$i],
                    'size' => $files['size'][$i]
                );

                $result = la_addons_process_dragdrop_file( $file_data, $form_options, $form_id, $field_name );

                if ( $result['success'] ) {
                    $response['files'][] = $result['file'];
                } else {
                    $response['errors'][] = $result['error'];
                }
            } else {
                $response['errors'][] = sprintf(
                    __( 'Upload error for file %s: %s', 'la-addons' ),
                    $files['name'][$i],
                    la_addons_get_upload_error_message( $files['error'][$i] )
                );
            }
        }

        $response['success'] = ! empty( $response['files'] );
    } else {
        $response['errors'][] = __( 'No files were uploaded.', 'la-addons' );
    }

    wp_send_json( $response );
}
add_action( 'wp_ajax_la_addons_dragdrop_upload', 'la_addons_handle_dragdrop_upload' );
add_action( 'wp_ajax_nopriv_la_addons_dragdrop_upload', 'la_addons_handle_dragdrop_upload' );

/**
 * Process individual file upload
 */
function la_addons_process_dragdrop_file( $file_data, $form_options = array(), $form_id = 0, $field_name = '' ) {
    try {
        // Validate file with form options
        $validation = la_addons_validate_dragdrop_file( $file_data, $form_options );
        if ( ! $validation['valid'] ) {
            return array(
                'success' => false,
                'error' => $validation['error']
            );
        }

        // Move uploaded file to WordPress uploads directory
        $upload_dir = wp_upload_dir();

        if ( $upload_dir['error'] ) {
            return array(
                'success' => false,
                'error' => __( 'Upload directory error: ', 'la-addons' ) . $upload_dir['error']
            );
        }

        // Generate filename based on form options
        $unique_filename = la_addons_generate_dragdrop_filename( $file_data['name'], $form_options, $form_id, $field_name );

        $new_file_path = $upload_dir['path'] . '/' . $unique_filename;

        // Move the uploaded file
        if ( ! move_uploaded_file( $file_data['tmp_name'], $new_file_path ) ) {
            return array(
                'success' => false,
                'error' => sprintf( __( 'Failed to move uploaded file: %s', 'la-addons' ), $file_data['name'] )
            );
        }

        // Get file mime type
        $wp_filetype = wp_check_filetype( $new_file_path );
        $mime_type = $wp_filetype['type'] ?: 'application/octet-stream';

        // Create WordPress attachment
        $attachment = array(
            'guid'           => $upload_dir['url'] . '/' . $unique_filename,
            'post_mime_type' => $mime_type,
            'post_title'     => $filename,
            'post_content'   => '',
            'post_status'    => 'inherit'
        );

        $attach_id = wp_insert_attachment( $attachment, $new_file_path );

        if ( is_wp_error( $attach_id ) ) {
            // Clean up the file if attachment creation failed
            unlink( $new_file_path );
            return array(
                'success' => false,
                'error' => sprintf( __( 'Failed to create attachment: %s', 'la-addons' ), $attach_id->get_error_message() )
            );
        }

        // Generate attachment metadata
        require_once( ABSPATH . 'wp-admin/includes/image.php' );
        $attach_data = wp_generate_attachment_metadata( $attach_id, $new_file_path );
        wp_update_attachment_metadata( $attach_id, $attach_data );

        // Get attachment URL
        $attachment_url = wp_get_attachment_url( $attach_id );

        return array(
            'success' => true,
            'file' => array(
                'name' => $file_data['name'],
                'size' => $file_data['size'],
                'type' => $mime_type,
                'url' => $attachment_url,
                'id' => $attach_id,
                'filename' => $unique_filename
            )
        );

    } catch ( Exception $e ) {
        error_log( 'LA Addons: Error processing drag-drop file: ' . $e->getMessage() );
        return array(
            'success' => false,
            'error' => __( 'An error occurred while processing the file.', 'la-addons' )
        );
    }
}

/**
 * Generate filename based on form options
 */
function la_addons_generate_dragdrop_filename( $original_name, $form_options = array(), $form_id = 0, $field_name = '' ) {
    $file_info = pathinfo( $original_name );
    $filename = sanitize_file_name( $file_info['filename'] );
    $extension = isset( $file_info['extension'] ) ? '.' . $file_info['extension'] : '';

    $naming_option = isset( $form_options['file_naming'] ) ? $form_options['file_naming'] : 'original';

    switch ( $naming_option ) {
        case 'timestamp':
            return $filename . '-' . time() . $extension;

        case 'custom':
            $pattern = isset( $form_options['custom_naming_pattern'] ) ? $form_options['custom_naming_pattern'] : '{name}-{timestamp}';
            $replacements = array(
                '{name}' => $filename,
                '{timestamp}' => time(),
                '{random}' => wp_generate_password( 8, false ),
                '{form_id}' => $form_id,
                '{field_name}' => $field_name
            );
            return str_replace( array_keys( $replacements ), array_values( $replacements ), $pattern ) . $extension;

        case 'original':
        default:
            // Add timestamp and random string to prevent conflicts while keeping original name
            return $filename . '-' . time() . '-' . wp_generate_password( 8, false ) . $extension;
    }
}

/**
 * Validate uploaded file
 */
function la_addons_validate_dragdrop_file( $file_data, $form_options = array() ) {
    // Check for upload errors
    if ( $file_data['error'] !== UPLOAD_ERR_OK ) {
        return array(
            'valid' => false,
            'error' => la_addons_get_upload_error_message( $file_data['error'] )
        );
    }

    // Validate filename security
    if ( ! la_addons_validate_filename_security( $file_data['name'] ) ) {
        return array(
            'valid' => false,
            'error' => sprintf( __( 'Filename "%s" contains suspicious characters or patterns.', 'la-addons' ), $file_data['name'] )
        );
    }

    // Check file size (use form options if available)
    $max_size = wp_max_upload_size();
    if ( ! empty( $form_options['max_file_size'] ) ) {
        $form_max_size = la_addons_parse_file_size( $form_options['max_file_size'] );
        if ( $form_max_size > 0 && $form_max_size < $max_size ) {
            $max_size = $form_max_size;
        }
    }

    if ( $file_data['size'] > $max_size ) {
        return array(
            'valid' => false,
            'error' => sprintf(
                __( 'File "%s" is too large. Maximum size is %s.', 'la-addons' ),
                $file_data['name'],
                size_format( $max_size )
            )
        );
    }

    // Check if file is empty
    if ( $file_data['size'] === 0 ) {
        return array(
            'valid' => false,
            'error' => sprintf( __( 'File "%s" is empty.', 'la-addons' ), $file_data['name'] )
        );
    }

    // Validate file type
    $wp_filetype = wp_check_filetype( $file_data['name'] );
    if ( ! $wp_filetype['type'] ) {
        return array(
            'valid' => false,
            'error' => sprintf( __( 'File type not allowed for "%s".', 'la-addons' ), $file_data['name'] )
        );
    }

    // Check against WordPress allowed mime types
    $allowed_mimes = get_allowed_mime_types();
    if ( ! in_array( $wp_filetype['type'], $allowed_mimes ) ) {
        return array(
            'valid' => false,
            'error' => sprintf( __( 'File type "%s" is not allowed by WordPress.', 'la-addons' ), $wp_filetype['type'] )
        );
    }

    // Check against form-specific allowed types (if configured)
    if ( ! empty( $form_options['allowed_types'] ) ) {
        if ( ! la_addons_is_file_type_allowed( $file_data['name'], $wp_filetype['type'], $form_options['allowed_types'] ) ) {
            return array(
                'valid' => false,
                'error' => sprintf( __( 'File type "%s" is not allowed for this field.', 'la-addons' ), $wp_filetype['type'] )
            );
        }
    }

    // Additional security check - scan file content (if enabled in form options)
    $security_scan_enabled = isset( $form_options['security_scan'] ) ? $form_options['security_scan'] : true;
    if ( $security_scan_enabled && ! la_addons_scan_file_content( $file_data['tmp_name'] ) ) {
        return array(
            'valid' => false,
            'error' => sprintf( __( 'File "%s" failed security scan.', 'la-addons' ), $file_data['name'] )
        );
    }

    return array( 'valid' => true );
}

/**
 * Parse file size string to bytes
 */
function la_addons_parse_file_size( $size_str ) {
    $units = array( 'B' => 1, 'KB' => 1024, 'MB' => 1024 * 1024, 'GB' => 1024 * 1024 * 1024 );
    $size_str = strtoupper( trim( $size_str ) );

    if ( preg_match( '/^(\d+(?:\.\d+)?)\s*(B|KB|MB|GB)$/', $size_str, $matches ) ) {
        $value = floatval( $matches[1] );
        $unit = $matches[2];
        return intval( $value * $units[ $unit ] );
    }

    // If no unit specified, assume bytes
    return intval( $size_str );
}

/**
 * Get human-readable upload error message
 */
function la_addons_get_upload_error_message( $error_code ) {
    $error_messages = array(
        UPLOAD_ERR_INI_SIZE   => __( 'The uploaded file exceeds the upload_max_filesize directive in php.ini.', 'la-addons' ),
        UPLOAD_ERR_FORM_SIZE  => __( 'The uploaded file exceeds the MAX_FILE_SIZE directive.', 'la-addons' ),
        UPLOAD_ERR_PARTIAL    => __( 'The uploaded file was only partially uploaded.', 'la-addons' ),
        UPLOAD_ERR_NO_FILE    => __( 'No file was uploaded.', 'la-addons' ),
        UPLOAD_ERR_NO_TMP_DIR => __( 'Missing a temporary folder.', 'la-addons' ),
        UPLOAD_ERR_CANT_WRITE => __( 'Failed to write file to disk.', 'la-addons' ),
        UPLOAD_ERR_EXTENSION  => __( 'A PHP extension stopped the file upload.', 'la-addons' ),
    );

    return isset( $error_messages[ $error_code ] )
        ? $error_messages[ $error_code ]
        : __( 'Unknown upload error.', 'la-addons' );
}

/**
 * Comprehensive file content security scan
 */
function la_addons_scan_file_content( $file_path ) {
    // Check if file exists and is readable
    if ( ! is_readable( $file_path ) ) {
        error_log( 'LA Addons: File not readable for security scan: ' . $file_path );
        return false;
    }

    // Get file size
    $file_size = filesize( $file_path );
    if ( $file_size === false || $file_size === 0 ) {
        error_log( 'LA Addons: Invalid file size for security scan: ' . $file_path );
        return false;
    }

    // Read file content (limit to first 8KB for performance)
    $read_size = min( $file_size, 8192 );
    $handle = fopen( $file_path, 'rb' );
    if ( ! $handle ) {
        error_log( 'LA Addons: Cannot open file for security scan: ' . $file_path );
        return false;
    }

    $content = fread( $handle, $read_size );
    fclose( $handle );

    if ( $content === false ) {
        error_log( 'LA Addons: Cannot read file content for security scan: ' . $file_path );
        return false;
    }

    // Enhanced malicious patterns detection
    $malicious_patterns = array(
        // PHP code patterns
        '/<\?php/i',
        '/<\?=/i',
        '/<%/i',

        // Script tags
        '/<script[^>]*>/i',
        '/<\/script>/i',

        // Dangerous PHP functions
        '/eval\s*\(/i',
        '/exec\s*\(/i',
        '/system\s*\(/i',
        '/shell_exec\s*\(/i',
        '/passthru\s*\(/i',
        '/file_get_contents\s*\(/i',
        '/file_put_contents\s*\(/i',
        '/fopen\s*\(/i',
        '/fwrite\s*\(/i',
        '/include\s*\(/i',
        '/require\s*\(/i',
        '/include_once\s*\(/i',
        '/require_once\s*\(/i',

        // Encoding/decoding functions that could hide malicious code
        '/base64_decode\s*\(/i',
        '/gzinflate\s*\(/i',
        '/gzuncompress\s*\(/i',
        '/str_rot13\s*\(/i',
        '/rawurldecode\s*\(/i',
        '/urldecode\s*\(/i',

        // SQL injection patterns
        '/union\s+select/i',
        '/drop\s+table/i',
        '/delete\s+from/i',
        '/insert\s+into/i',
        '/update\s+set/i',

        // JavaScript patterns
        '/document\.write/i',
        '/window\.location/i',
        '/eval\s*\(/i',

        // Common malware signatures
        '/c99shell/i',
        '/r57shell/i',
        '/webshell/i',
        '/backdoor/i',
        '/malware/i',

        // Suspicious hex patterns
        '/\\x[0-9a-f]{2}/i',

        // Suspicious base64 patterns (long encoded strings)
        '/[A-Za-z0-9+\/]{100,}={0,2}/',
    );

    // Check for malicious patterns
    foreach ( $malicious_patterns as $pattern ) {
        if ( preg_match( $pattern, $content ) ) {
            error_log( 'LA Addons: Potentially malicious content detected in file: ' . $file_path . ' (Pattern: ' . $pattern . ')' );
            return false;
        }
    }

    // Check for suspicious file headers
    if ( ! la_addons_validate_file_header( $file_path, $content ) ) {
        return false;
    }

    // Check for embedded executables
    if ( la_addons_contains_executable_code( $content ) ) {
        error_log( 'LA Addons: Executable code detected in file: ' . $file_path );
        return false;
    }

    return true;
}

/**
 * Validate file header matches expected file type
 */
function la_addons_validate_file_header( $file_path, $content ) {
    $file_extension = strtolower( pathinfo( $file_path, PATHINFO_EXTENSION ) );

    // Define expected file signatures (magic numbers)
    $file_signatures = array(
        'jpg'  => array( 'FFD8FF' ),
        'jpeg' => array( 'FFD8FF' ),
        'png'  => array( '89504E47' ),
        'gif'  => array( '474946383761', '474946383961' ), // GIF87a, GIF89a
        'pdf'  => array( '25504446' ), // %PDF
        'zip'  => array( '504B0304', '504B0506', '504B0708' ),
        'doc'  => array( 'D0CF11E0A1B11AE1' ),
        'docx' => array( '504B0304' ), // DOCX is ZIP-based
        'xls'  => array( 'D0CF11E0A1B11AE1' ),
        'xlsx' => array( '504B0304' ), // XLSX is ZIP-based
        'ppt'  => array( 'D0CF11E0A1B11AE1' ),
        'pptx' => array( '504B0304' ), // PPTX is ZIP-based
    );

    if ( isset( $file_signatures[ $file_extension ] ) ) {
        $expected_signatures = $file_signatures[ $file_extension ];
        $file_header = strtoupper( bin2hex( substr( $content, 0, 16 ) ) );

        $valid_header = false;
        foreach ( $expected_signatures as $signature ) {
            if ( strpos( $file_header, $signature ) === 0 ) {
                $valid_header = true;
                break;
            }
        }

        if ( ! $valid_header ) {
            error_log( 'LA Addons: File header mismatch for: ' . $file_path . ' (Expected: ' . implode( ' or ', $expected_signatures ) . ', Got: ' . substr( $file_header, 0, 16 ) . ')' );
            return false;
        }
    }

    return true;
}

/**
 * Check for embedded executable code
 */
function la_addons_contains_executable_code( $content ) {
    // Check for PE (Windows executable) header
    if ( strpos( $content, "MZ" ) === 0 ) {
        return true;
    }

    // Check for ELF (Linux executable) header
    if ( strpos( $content, "\x7FELF" ) === 0 ) {
        return true;
    }

    // Check for Mach-O (macOS executable) header
    if ( strpos( $content, "\xFE\xED\xFA\xCE" ) === 0 || strpos( $content, "\xFE\xED\xFA\xCF" ) === 0 ) {
        return true;
    }

    return false;
}

/**
 * Check if file type is allowed based on form configuration
 */
function la_addons_is_file_type_allowed( $filename, $mime_type, $allowed_types_string ) {
    $allowed_types = array_map( 'trim', explode( ',', $allowed_types_string ) );
    $file_extension = '.' . strtolower( pathinfo( $filename, PATHINFO_EXTENSION ) );

    foreach ( $allowed_types as $allowed_type ) {
        $allowed_type = trim( $allowed_type );

        // Check for wildcard MIME types (e.g., image/*, video/*)
        if ( strpos( $allowed_type, '*' ) !== false ) {
            $base_type = str_replace( '*', '', $allowed_type );
            if ( strpos( $mime_type, $base_type ) === 0 ) {
                return true;
            }
        }
        // Check for exact MIME type match
        elseif ( $mime_type === $allowed_type ) {
            return true;
        }
        // Check for file extension match
        elseif ( $allowed_type === $file_extension ) {
            return true;
        }
        // Check for extension without dot
        elseif ( '.' . $allowed_type === $file_extension ) {
            return true;
        }
    }

    return false;
}

/**
 * Additional security: Check for double extensions and suspicious patterns
 */
function la_addons_validate_filename_security( $filename ) {
    // Convert to lowercase for checking
    $lower_filename = strtolower( $filename );

    // Check for double extensions (e.g., file.php.jpg)
    $dangerous_extensions = array( 'php', 'php3', 'php4', 'php5', 'phtml', 'asp', 'aspx', 'jsp', 'js', 'exe', 'bat', 'cmd', 'com', 'scr', 'vbs', 'jar' );

    foreach ( $dangerous_extensions as $ext ) {
        if ( strpos( $lower_filename, '.' . $ext . '.' ) !== false ) {
            error_log( 'LA Addons: Dangerous double extension detected: ' . $filename );
            return false;
        }
    }

    // Check for null bytes (directory traversal attempt)
    if ( strpos( $filename, "\0" ) !== false ) {
        error_log( 'LA Addons: Null byte detected in filename: ' . $filename );
        return false;
    }

    // Check for directory traversal patterns
    if ( strpos( $filename, '../' ) !== false || strpos( $filename, '..\\' ) !== false ) {
        error_log( 'LA Addons: Directory traversal attempt detected: ' . $filename );
        return false;
    }

    // Check for suspicious characters
    $suspicious_chars = array( '<', '>', '"', '|', '?', '*', ':' );
    foreach ( $suspicious_chars as $char ) {
        if ( strpos( $filename, $char ) !== false ) {
            error_log( 'LA Addons: Suspicious character detected in filename: ' . $filename );
            return false;
        }
    }

    return true;
}
