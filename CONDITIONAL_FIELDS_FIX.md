# Conditional Fields Fix Documentation

## Overview

This document outlines the issues found with the conditional fields functionality and the fixes that have been implemented.

## Issues Identified

### 1. **API Endpoint Mismatch** (Critical)
- **Problem**: Frontend JavaScript was trying to fetch conditional logic from `/feedback` endpoint
- **Root Cause**: Incorrect API endpoint in the fetch request
- **Impact**: Conditional logic rules were never loaded on the frontend

### 2. **Incorrect Logic Handling** (Major)
- **Problem**: Frontend JavaScript didn't properly handle "any" logic type
- **Root Cause**: Missing case for "any" in the logic evaluation
- **Impact**: OR conditions didn't work correctly

### 3. **Field Detection Issues** (Major)
- **Problem**: Form fields weren't being detected properly in some cases
- **Root Cause**: Incomplete regex patterns and missing fallback methods
- **Impact**: Admin UI couldn't find fields to configure conditional logic

### 4. **Event Handling and Timing** (Major)
- **Problem**: Conditional logic wasn't initialized properly for dynamically loaded forms
- **Root Cause**: Only listening to DOMContentLoaded event
- **Impact**: Forms loaded after page load didn't have conditional logic

### 5. **Missing Error Handling** (Minor)
- **Problem**: No debugging or error handling for troubleshooting
- **Root Cause**: Minimal logging and error checking
- **Impact**: Difficult to diagnose issues

## Fixes Implemented

### 1. API Endpoint Fix
- **File**: `includes/conditional-fields-api.php`
- **Changes**: 
  - Added new REST endpoint: `/wp-json/la-addons/v1/forms/{id}/conditional-logic`
  - Maintained backward compatibility with REST field approach
  - Added proper error handling and validation

### 2. Frontend JavaScript Improvements
- **File**: `assets/js/conditional-fields.js`
- **Changes**:
  - Updated API endpoint URL
  - Fixed logic handling for "any" conditions
  - Added comprehensive error handling and debugging
  - Improved event handling with multiple initialization triggers
  - Added fallback method for API calls
  - Enhanced field wrapper detection

### 3. Field Detection Enhancement
- **File**: `includes/conditional-fields-api.php`
- **Changes**:
  - Improved regex patterns for field detection
  - Added multiple fallback methods for getting form content
  - Enhanced error logging for debugging

### 4. Event Handling Improvements
- **File**: `assets/js/conditional-fields.js`
- **Changes**:
  - Added MutationObserver for dynamic form detection
  - Added CF7 event listeners (wpcf7mailsent, wpcf7invalid)
  - Prevented duplicate initialization with flags
  - Added proper timing with setTimeout for form reloads

### 5. CSS and Styling
- **File**: `assets/css/admin-style.css`
- **Changes**:
  - Added frontend conditional field styles
  - Added smooth transitions for show/hide effects
  - Added utility classes for conditional visibility

### 6. Script Enqueuing
- **File**: `includes/enqueue-scripts.php`
- **Changes**:
  - Added frontend enqueuing for conditional fields CSS and JS
  - Ensured proper loading order and dependencies

## Testing

### Backend Tests
Run the backend test file to verify server-side functionality:

```bash
php tests/test-conditional-fields-backend.php
```

This tests:
- Field scanning functionality
- Data structure validation
- API response format
- Data sanitization
- Operator validation

### Frontend Tests
Open `tests/conditional-fields-test.html` in a browser to test frontend functionality:

1. Open the test file in a web browser
2. Click "Run Tests" to execute automated tests
3. Manually test the form by changing the trigger field value
4. Verify that the conditional field shows/hides correctly

### Manual Testing Steps

1. **Create a Contact Form 7 form** with multiple fields
2. **Go to the Conditional Logic tab** in the form editor
3. **Add a rule group** with:
   - Target field: Select a field to show/hide
   - Action: Show or Hide
   - Logic: All conditions match (AND) or Any condition matches (OR)
   - Conditions: Add one or more conditions
4. **Save the form**
5. **View the form on the frontend**
6. **Test the conditional logic** by changing field values

### Debugging

Enable debugging by opening browser console (F12) when viewing forms with conditional logic. You should see:
- "Found X CF7 forms to initialize"
- "Conditional logic response: ..." 
- "Evaluating X rule groups"
- Individual condition evaluation results

## Browser Compatibility

The fixes are compatible with:
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## Performance Considerations

- MutationObserver is used efficiently with debouncing
- API calls are cached per form
- Event listeners are properly cleaned up
- CSS transitions are optimized

## Future Improvements

1. **Add visual feedback** in admin UI when fields aren't detected
2. **Implement field dependency validation** to prevent circular references
3. **Add support for more complex operators** (regex, date comparisons)
4. **Create a visual rule builder** with drag-and-drop interface
5. **Add import/export functionality** for conditional logic rules

## Troubleshooting

### Common Issues

1. **Conditional logic not working**
   - Check browser console for errors
   - Verify API endpoint is accessible
   - Ensure form fields have proper names

2. **Fields not detected in admin**
   - Check if form content is saved properly
   - Verify field names don't contain special characters
   - Check error logs for field detection issues

3. **Rules not saving**
   - Verify user permissions
   - Check for JavaScript errors in admin
   - Ensure all required fields are filled

### Debug Mode

To enable verbose debugging, add this to your theme's functions.php:

```php
add_action('wp_footer', function() {
    if (current_user_can('manage_options')) {
        echo '<script>window.laAddonsDebug = true;</script>';
    }
});
```

## Support

For additional support or to report issues:
1. Check the browser console for error messages
2. Enable WordPress debug logging
3. Review the test files for expected behavior
4. Check the conditional logic rules in the database (`_la_addons_conditional_logic` meta key)
