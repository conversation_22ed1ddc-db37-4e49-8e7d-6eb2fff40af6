document.addEventListener("DOMContentLoaded", function () {
  console.log("signature-pad-init.js loaded and DOMContentLoaded fired.");
  var signaturePadCanvas = document.querySelector(
    ".la-addons-signature-pad canvas"
  );

  if (signaturePadCanvas) {
    console.log("Signature pad canvas found. Initializing SignaturePad.");

    // Get form ID from data attribute or another method
    var formId = document.querySelector('input[name="_wpcf7"]')?.value;
    console.log("Form ID detected:", formId);

    // Fetch signature options via AJAX
    if (formId) {
      fetch(laAddonsSignature.ajaxUrl, {
        method: "POST",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        body: "action=get_signature_options&form_id=" + formId,
      })
        .then((response) => response.json())
        .then((data) => {
          console.log("Signature options received:", data);
          if (data.success && data.data) {
            initSignaturePad(data.data);
          } else {
            console.error("Invalid data format received:", data);
            initSignaturePad({});
          }
        })
        .catch((error) => {
          console.error("Error fetching signature options:", error);
          initSignaturePad({});
        });
    } else {
      initSignaturePad({});
    }

    function initSignaturePad(options) {
      console.log("Initializing signature pad with options:", options);

      // Use the values from options instead of hard-coded values
      // Make sure we have valid numbers
      var padWidth = parseInt(options.pad_width) || 300;
      var padHeight = parseInt(options.pad_height) || 100;

      console.log("Setting canvas dimensions to:", padWidth, "x", padHeight);

      // Set canvas dimensions directly
      signaturePadCanvas.width = padWidth;
      signaturePadCanvas.height = padHeight;

      // Also set CSS dimensions explicitly
      signaturePadCanvas.style.width = padWidth + "px";
      signaturePadCanvas.style.height = padHeight + "px";

      console.log(
        "Canvas dimensions after setting:",
        "width:",
        signaturePadCanvas.width,
        "height:",
        signaturePadCanvas.height,
        "style.width:",
        signaturePadCanvas.style.width,
        "style.height:",
        signaturePadCanvas.style.height
      );

      // Set canvas background color
      if (options.pad_bg_color) {
        signaturePadCanvas.style.backgroundColor = options.pad_bg_color;
      }

      // Clear the canvas to apply the background color
      var ctx = signaturePadCanvas.getContext("2d");
      ctx.fillStyle = options.pad_bg_color || "#dddddd";
      ctx.fillRect(0, 0, signaturePadCanvas.width, signaturePadCanvas.height);

      // Create a new SignaturePad instance
      var signaturePad = new SignaturePad(signaturePadCanvas, {
        penColor: options.pen_color || "#000000",
        backgroundColor: options.pad_bg_color || "#dddddd",
      });

      // Find the hidden input field
      var hiddenInput = signaturePadCanvas
        .closest(".la-addons-signature-pad")
        .querySelector('input[type="hidden"]');

      // Update hidden input when signature changes
      signaturePad.onEnd = function () {
        hiddenInput.value = signaturePad.toDataURL();
        console.log("Signature captured and hidden input updated.");
      };

      // Add a clear button if it doesn't exist already
      var existingClearButton = signaturePadCanvas.parentNode.querySelector(
        ".signature-clear-button"
      );
      if (!existingClearButton) {
        var clearButton = document.createElement("button");
        clearButton.textContent = "Clear";
        clearButton.type = "button";
        clearButton.className = "signature-clear-button";
        clearButton.style.marginTop = "5px";
        clearButton.addEventListener("click", function () {
          signaturePad.clear();
          hiddenInput.value = "";
        });

        signaturePadCanvas.parentNode.appendChild(clearButton);
      }
    }
  } else {
    console.log("Signature pad canvas not found.");
  }
});
