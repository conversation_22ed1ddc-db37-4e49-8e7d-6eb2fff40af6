/* CF7 Editor Tabs */
.cf7-editor-tabs .ui-tabs-nav {
  display: flex;
  margin: 0;
  padding: 0;
  border-bottom: 1px solid #ccc;
}

.cf7-editor-tabs .ui-tabs-nav li {
  margin: 0;
  padding: 0;
  list-style: none;
}

.cf7-editor-tabs .ui-tabs-nav li a {
  display: block;
  padding: 10px 15px;
  text-decoration: none;
  color: #555;
  border: 1px solid transparent;
  border-bottom: none;
}

.cf7-editor-tabs .ui-tabs-nav li.ui-tabs-active a {
  border-color: #ccc;
  border-bottom-color: #f1f1f1;
  background: #f1f1f1;
}

/* Signature Options Panel */
#signature-options-panel {
  padding: 15px;
  background: #fff;
  border: 1px solid #ccc;
  border-top: none;
}

/* Frontend Signature Pad Styling */
.la-addons-signature-pad {
  margin-bottom: 20px;
}

.la-addons-signature-pad canvas {
  cursor: crosshair;
  border: 1px solid #ccc !important;
  display: block !important;
  box-sizing: border-box !important;
  margin-bottom: 5px !important;
  /* Remove any max-width constraints */
  max-width: none !important;
}

.signature-clear-button {
  background: #f7f7f7;
  border: 1px solid #ccc;
  padding: 5px 10px;
  cursor: pointer;
  font-size: 14px;
}

.signature-clear-button:hover {
  background: #f0f0f0;
}

/* Submission details styling */
.submission-details td {
  padding: 0;
}

.submission-details table {
  margin: 10px;
  border: 1px solid #e5e5e5;
}

.submission-details table th {
  background-color: #f9f9f9;
  font-weight: 600;
}

.submission-details table td,
.submission-details table th {
  padding: 8px 10px;
}

/* Improve filter form layout */
.wrap form {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  gap: 10px;
  flex-wrap: wrap;
}

.wrap form input[type="search"],
.wrap form select,
.wrap form input[type="date"] {
  min-width: 180px;
}

/* Make the table more readable */
.wp-list-table th {
  font-weight: 600;
}

.wp-list-table .toggle-details {
  margin: 0;
}

/* Conditional Logic UI Styling */
.la-addons-panel {
  background: #fff;
  border: 1px solid #e5e5e5;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
  padding: 15px;
  margin-top: 15px;
}

.rule-group {
  background: #f9f9f9;
  border: 1px solid #e5e5e5;
  margin-bottom: 20px;
  padding: 15px;
  border-radius: 3px;
}

.rule-group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  border-bottom: 1px solid #e5e5e5;
  padding-bottom: 10px;
}

.rule-group-header h3 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
}

.target-field-container {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
  padding: 10px;
  background: #fff;
  border-radius: 3px;
  border: 1px solid #e5e5e5;
}

.conditions-container {
  margin-bottom: 15px;
}

.condition-row {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
  padding: 10px;
  background: #fff;
  border-radius: 3px;
  border: 1px solid #e5e5e5;
}

.condition-field-select,
.operator-select {
  min-width: 150px;
}

.condition-value-input {
  flex: 1;
  min-width: 150px;
}

.rule-actions {
  margin-top: 20px;
}

.no-rules-message {
  background: #f7f7f7;
  border-left: 4px solid #0073aa;
  padding: 10px 15px;
  margin-bottom: 20px;
}

/* Responsive adjustments */
@media screen and (max-width: 782px) {
  .target-field-container,
  .condition-row {
    flex-direction: column;
    align-items: flex-start;
  }

  .condition-field-select,
  .operator-select,
  .condition-value-input {
    width: 100%;
  }
}

/* Tooltips for better UX */
.la-addons-tooltip {
  position: relative;
  display: inline-block;
  margin-left: 5px;
  cursor: help;
}

.la-addons-tooltip .dashicons {
  color: #0073aa;
  font-size: 16px;
  width: 16px;
  height: 16px;
}

/* Ensure templates are completely hidden */
#rule-templates {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
  position: absolute !important;
  left: -9999px !important;
}

/* Frontend conditional field styles */
.la-addons-hidden {
  display: none !important;
  visibility: hidden !important;
}

.la-addons-visible {
  display: block !important;
  visibility: visible !important;
}

/* Smooth transitions for conditional fields */
.wpcf7-form p,
.wpcf7-form div {
  transition: opacity 0.3s ease-in-out;
}

.la-addons-hidden {
  opacity: 0;
  transition: opacity 0.3s ease-in-out;
}

.la-addons-tooltip .tooltip-text {
  visibility: hidden;
  width: 200px;
  background-color: #333;
  color: #fff;
  text-align: center;
  border-radius: 6px;
  padding: 5px;
  position: absolute;
  z-index: 1;
  bottom: 125%;
  left: 50%;
  margin-left: -100px;
  opacity: 0;
  transition: opacity 0.3s;
}

.la-addons-tooltip:hover .tooltip-text {
  visibility: visible;
  opacity: 1;
}

/* Drag and Drop Upload Styling */
.la-addons-dragdrop-upload {
  margin-bottom: 20px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

.la-dragdrop-zone {
  border: 2px dashed #d1d5db;
  border-radius: 8px;
  padding: 40px 20px;
  text-align: center;
  background-color: #f9fafb;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  min-height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.la-dragdrop-zone:hover {
  border-color: #3b82f6;
  background-color: #eff6ff;
}

.la-dragdrop-zone.la-dragdrop-dragover {
  border-color: #10b981;
  background-color: #ecfdf5;
  transform: scale(1.02);
}

.la-dragdrop-zone.la-dragdrop-disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background-color: #f3f4f6;
}

.la-dragdrop-zone.la-dragdrop-disabled:hover {
  border-color: #d1d5db;
  background-color: #f3f4f6;
  transform: none;
}

.la-dragdrop-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.la-dragdrop-icon {
  color: #6b7280;
  transition: color 0.3s ease;
}

.la-dragdrop-zone:hover .la-dragdrop-icon {
  color: #3b82f6;
}

.la-dragdrop-zone.la-dragdrop-dragover .la-dragdrop-icon {
  color: #10b981;
}

.la-dragdrop-text {
  color: #374151;
}

.la-dragdrop-primary {
  font-size: 16px;
  font-weight: 500;
  margin: 0 0 8px 0;
}

.la-dragdrop-secondary {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
}

.la-dragdrop-browse {
  background: none;
  border: none;
  color: #3b82f6;
  text-decoration: underline;
  cursor: pointer;
  font-size: inherit;
  font-weight: 500;
  padding: 0;
}

.la-dragdrop-browse:hover {
  color: #1d4ed8;
}

/* File List Styling */
.la-dragdrop-file-list {
  margin-top: 16px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background-color: #ffffff;
  overflow: hidden;
}

.la-file-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f3f4f6;
  transition: background-color 0.2s ease;
}

.la-file-item:last-child {
  border-bottom: none;
}

.la-file-item:hover {
  background-color: #f9fafb;
}

/* File Preview Styling */
.la-file-preview {
  flex-shrink: 0;
  margin-right: 12px;
  position: relative;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.la-file-icon {
  color: #6b7280;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.la-file-thumbnail {
  width: 48px;
  height: 48px;
  border-radius: 6px;
  overflow: hidden;
  background-color: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #e5e7eb;
}

.la-file-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.la-file-thumbnail.loaded img {
  opacity: 1;
}

.la-status-indicator {
  position: absolute;
  top: -4px;
  right: -4px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: #ffffff;
  border: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  z-index: 1;
}

.la-status-pending {
  background-color: #fef3c7;
  border-color: #f59e0b;
}

.la-status-uploading {
  background-color: #dbeafe;
  border-color: #3b82f6;
}

.la-status-uploaded {
  background-color: #d1fae5;
  border-color: #10b981;
}

.la-status-error {
  background-color: #fee2e2;
  border-color: #ef4444;
}

.la-file-info {
  flex: 1;
  min-width: 0;
}

.la-file-name {
  font-weight: 500;
  color: #111827;
  margin-bottom: 4px;
  word-break: break-word;
  font-size: 14px;
  line-height: 1.3;
}

.la-file-details {
  display: flex;
  gap: 8px;
  align-items: center;
  flex-wrap: wrap;
}

.la-file-size {
  font-size: 11px;
  color: #6b7280;
  background-color: #f3f4f6;
  padding: 2px 6px;
  border-radius: 3px;
}

.la-file-type {
  font-size: 10px;
  color: #374151;
  background-color: #e5e7eb;
  padding: 2px 6px;
  border-radius: 3px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.la-file-actions {
  flex-shrink: 0;
  margin-left: 12px;
  display: flex;
  gap: 4px;
  align-items: center;
}

.la-file-view {
  background: none;
  border: none;
  color: #3b82f6;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
}

.la-file-view:hover {
  background-color: #eff6ff;
  color: #1d4ed8;
}

.la-file-remove {
  background: none;
  border: none;
  color: #ef4444;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.la-file-remove:hover {
  background-color: #fef2f2;
  color: #dc2626;
}

/* File Status Styling */
.la-file-status-pending .la-file-icon {
  color: #f59e0b;
}

.la-file-status-uploading .la-file-icon {
  color: #3b82f6;
}

.la-file-status-uploaded .la-file-icon {
  color: #10b981;
}

.la-file-status-error .la-file-icon {
  color: #ef4444;
}

.la-file-status-error .la-file-name {
  color: #ef4444;
}

/* Progress Bar Styling */
.la-file-progress {
  width: 100%;
  height: 4px;
  background-color: #e5e7eb;
  border-radius: 2px;
  margin-top: 4px;
  overflow: hidden;
}

.la-file-progress-bar {
  height: 100%;
  background-color: #3b82f6;
  border-radius: 2px;
  transition: width 0.3s ease;
}

/* Overall Progress Styling */
.la-dragdrop-progress {
  margin-top: 16px;
  padding: 16px;
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
}

.la-progress-bar {
  width: 100%;
  height: 8px;
  background-color: #e5e7eb;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 8px;
}

.la-progress-fill {
  height: 100%;
  background-color: #3b82f6;
  border-radius: 4px;
  transition: width 0.3s ease;
}

.la-progress-text {
  font-size: 14px;
  color: #374151;
  text-align: center;
}

/* Error Styling */
.la-dragdrop-errors {
  margin-top: 12px;
}

.la-dragdrop-error {
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  color: #dc2626;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 14px;
  margin-bottom: 8px;
}

.la-dragdrop-error:last-child {
  margin-bottom: 0;
}

/* Responsive Design for Drag-Drop Upload */
@media screen and (max-width: 768px) {
  .la-dragdrop-zone {
    padding: 30px 15px;
    min-height: 100px;
  }

  .la-dragdrop-primary {
    font-size: 14px;
  }

  .la-dragdrop-secondary {
    font-size: 12px;
  }

  .la-dragdrop-icon svg {
    width: 36px;
    height: 36px;
  }

  .la-file-item {
    padding: 10px 12px;
  }

  .la-file-name {
    font-size: 14px;
  }

  .la-file-size {
    font-size: 11px;
  }

  .la-dragdrop-progress {
    padding: 12px;
  }

  .la-progress-text {
    font-size: 12px;
  }
}

@media screen and (max-width: 480px) {
  .la-dragdrop-zone {
    padding: 20px 10px;
    min-height: 80px;
  }

  .la-dragdrop-content {
    gap: 12px;
  }

  .la-dragdrop-primary {
    font-size: 13px;
  }

  .la-dragdrop-secondary {
    font-size: 11px;
  }

  .la-dragdrop-icon svg {
    width: 32px;
    height: 32px;
  }

  .la-file-item {
    padding: 8px 10px;
    flex-wrap: wrap;
  }

  .la-file-info {
    margin-right: 8px;
  }

  .la-file-actions {
    margin-left: 0;
    margin-top: 4px;
    width: 100%;
    display: flex;
    justify-content: flex-end;
  }

  .la-dragdrop-error {
    font-size: 12px;
    padding: 6px 10px;
  }
}

/* Dark mode support (if theme supports it) */
@media (prefers-color-scheme: dark) {
  .la-dragdrop-zone {
    background-color: #1f2937;
    border-color: #4b5563;
  }

  .la-dragdrop-zone:hover {
    background-color: #111827;
    border-color: #60a5fa;
  }

  .la-dragdrop-zone.la-dragdrop-dragover {
    background-color: #064e3b;
    border-color: #34d399;
  }

  .la-dragdrop-text {
    color: #d1d5db;
  }

  .la-dragdrop-secondary {
    color: #9ca3af;
  }

  .la-dragdrop-browse {
    color: #60a5fa;
  }

  .la-dragdrop-browse:hover {
    color: #93c5fd;
  }

  .la-dragdrop-file-list {
    background-color: #1f2937;
    border-color: #4b5563;
  }

  .la-file-item {
    border-color: #374151;
  }

  .la-file-item:hover {
    background-color: #111827;
  }

  .la-file-name {
    color: #f9fafb;
  }

  .la-file-size {
    color: #9ca3af;
  }

  .la-dragdrop-progress {
    background-color: #1f2937;
    border-color: #4b5563;
  }

  .la-progress-text {
    color: #d1d5db;
  }
}

/* Animation for file upload states */
@keyframes la-pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.la-file-status-uploading .la-file-icon {
  animation: la-pulse 1.5s ease-in-out infinite;
}

/* Accessibility improvements */
.la-dragdrop-zone:focus-within {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.la-file-remove:focus {
  outline: 2px solid #ef4444;
  outline-offset: 2px;
}

/* Print styles */
@media print {
  .la-addons-dragdrop-upload {
    display: none;
  }
}

/* Admin form validation styles */
.form-table input.error,
.form-table textarea.error {
  border-color: #dc3232;
  box-shadow: 0 0 2px rgba(220, 50, 50, 0.3);
}

.form-table input.error:focus,
.form-table textarea.error:focus {
  border-color: #dc3232;
  box-shadow: 0 0 5px rgba(220, 50, 50, 0.3);
}

/* Admin panel specific styles */
.la-addons-panel {
  background: #ffffff;
  border: 1px solid #c3c4c7;
  border-radius: 4px;
  padding: 20px;
  margin-top: 20px;
}

.la-addons-panel h2 {
  margin-top: 0;
  color: #1d2327;
  font-size: 18px;
  font-weight: 600;
}

.la-addons-panel-footer {
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid #f0f0f1;
}

.la-addons-panel-footer .description {
  font-style: italic;
  color: #646970;
}
