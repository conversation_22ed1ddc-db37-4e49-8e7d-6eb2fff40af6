<?php

function la_addons_save_submission( $contact_form ) {
    // Temporary bypass for debugging - uncomment the next line to disable submission processing
    // return;

    try {
        $submission = WPCF7_Submission::get_instance();

        if ( !$submission ) {
            error_log('LA Addons: No submission instance found');
            return;
        }

        $form_data = $submission->get_posted_data();
        $form_id = $contact_form->id();
        $uploaded_files = $submission->uploaded_files();

        error_log('LA Addons: Processing submission for form ID: ' . $form_id);
        error_log('LA Addons: Uploaded files structure: ' . print_r($uploaded_files, true));

        // Process signature fields
        foreach ( $form_data as $field_name => $field_value ) {
            if ( is_string($field_value) && strpos( $field_value, 'data:image/png;base64,' ) === 0 ) {
                try {
                    $img = str_replace('data:image/png;base64,', '', $field_value);
                    $img = str_replace(' ', '+', $img);
                    $data = base64_decode($img);

                    if ( $data === false ) {
                        error_log('LA Addons: Failed to decode base64 signature data');
                        continue;
                    }

                    $file_name = 'signature-' . time() . '-' . wp_generate_password(8, false) . '.png';
                    $upload_dir = wp_upload_dir();

                    if ( $upload_dir['error'] ) {
                        error_log('LA Addons: Upload directory error: ' . $upload_dir['error']);
                        continue;
                    }

                    $new_file_path = $upload_dir['path'] . '/' . $file_name;

                    if ( file_put_contents($new_file_path, $data) === false ) {
                        error_log('LA Addons: Failed to write signature file: ' . $new_file_path);
                        continue;
                    }

                    $attachment = array(
                        'guid'           => $new_file_path,
                        'post_mime_type' => 'image/png', // Use known mime type instead of mime_content_type()
                        'post_title'     => preg_replace( '/\.[^.]+$/', '', $file_name ),
                        'post_content'   => '',
                        'post_status'    => 'inherit'
                    );

                    $attach_id = wp_insert_attachment( $attachment, $new_file_path );

                    if ( $attach_id && !is_wp_error($attach_id) ) {
                        require_once( ABSPATH . 'wp-admin/includes/image.php' );
                        $attach_data = wp_generate_attachment_metadata( $attach_id, $new_file_path );
                        wp_update_attachment_metadata( $attach_id, $attach_data );
                        $form_data[$field_name] = wp_get_attachment_url( $attach_id );
                        error_log('LA Addons: Signature saved successfully: ' . $form_data[$field_name]);
                    } else {
                        error_log('LA Addons: Failed to create attachment for signature');
                    }
                } catch (Exception $e) {
                    error_log('LA Addons: Error processing signature: ' . $e->getMessage());
                }
            }
        }

        // Process drag-and-drop uploaded files first
        foreach ( $form_data as $field_name => $field_value ) {
            if ( is_array( $field_value ) ) {
                $processed_dragdrop_files = array();
                $has_dragdrop_files = false;

                foreach ( $field_value as $item ) {
                    // Handle JSON-encoded drag-drop file data
                    if ( is_string( $item ) ) {
                        $decoded_item = json_decode( $item, true );
                        if ( is_array( $decoded_item ) && isset( $decoded_item['la_dragdrop_file'] ) && $decoded_item['la_dragdrop_file'] === true ) {
                            $has_dragdrop_files = true;

                            // Verify attachment still exists
                            if ( isset( $decoded_item['attachment_id'] ) && $decoded_item['attachment_id'] ) {
                                $attachment_url = wp_get_attachment_url( $decoded_item['attachment_id'] );
                                if ( $attachment_url ) {
                                    $processed_dragdrop_files[] = array(
                                        'url' => $attachment_url,
                                        'filename' => $decoded_item['filename'] ?? '',
                                        'original_name' => $decoded_item['original_name'] ?? '',
                                        'size' => $decoded_item['size'] ?? 0,
                                        'type' => $decoded_item['type'] ?? '',
                                        'attachment_id' => $decoded_item['attachment_id']
                                    );
                                    error_log('LA Addons: Drag-drop file processed: ' . $attachment_url);
                                } else {
                                    error_log('LA Addons: Drag-drop attachment not found: ' . $decoded_item['attachment_id']);
                                }
                            }
                        }
                    }
                    // Handle direct array format (legacy support)
                    elseif ( is_array( $item ) && isset( $item['la_dragdrop_file'] ) && $item['la_dragdrop_file'] === true ) {
                        $has_dragdrop_files = true;

                        if ( isset( $item['attachment_id'] ) && $item['attachment_id'] ) {
                            $attachment_url = wp_get_attachment_url( $item['attachment_id'] );
                            if ( $attachment_url ) {
                                $processed_dragdrop_files[] = array(
                                    'url' => $attachment_url,
                                    'filename' => $item['filename'] ?? '',
                                    'original_name' => $item['original_name'] ?? '',
                                    'size' => $item['size'] ?? 0,
                                    'type' => $item['type'] ?? '',
                                    'attachment_id' => $item['attachment_id']
                                );
                                error_log('LA Addons: Drag-drop file processed: ' . $attachment_url);
                            }
                        }
                    }
                }

                // If we found drag-drop files, process them
                if ( $has_dragdrop_files && ! empty( $processed_dragdrop_files ) ) {
                    // Store file information in a structured way
                    $file_urls = array_column( $processed_dragdrop_files, 'url' );

                    // For form data, store URLs (compatible with existing systems)
                    $form_data[$field_name] = count( $file_urls ) === 1 ? $file_urls[0] : implode( ', ', $file_urls );

                    // Store detailed file information for admin/export purposes
                    $form_data[$field_name . '_dragdrop_details'] = $processed_dragdrop_files;

                    error_log('LA Addons: Processed ' . count( $processed_dragdrop_files ) . ' drag-drop files for field: ' . $field_name);
                }
            }
        }

        // Process uploaded files
        foreach ( $uploaded_files as $field_name => $file_data ) {
            try {
                // Handle both string paths and arrays
                $file_path = is_array($file_data) ? $file_data[0] : $file_data;

                if ( !is_string($file_path) || !file_exists($file_path) ) {
                    error_log('LA Addons: Invalid or non-existent uploaded file: ' . print_r($file_data, true));
                    continue;
                }

                $file_name = basename( $file_path );
                $upload_dir = wp_upload_dir();

                if ( $upload_dir['error'] ) {
                    error_log('LA Addons: Upload directory error: ' . $upload_dir['error']);
                    continue;
                }

                $new_file_path = $upload_dir['path'] . '/' . $file_name;

                if ( !copy( $file_path, $new_file_path ) ) {
                    error_log('LA Addons: Failed to copy uploaded file: ' . $file_path);
                    continue;
                }

                // Get mime type safely
                $mime_type = 'application/octet-stream'; // Default
                if ( function_exists('mime_content_type') ) {
                    $detected_mime = mime_content_type( $new_file_path );
                    if ( $detected_mime ) {
                        $mime_type = $detected_mime;
                    }
                } else {
                    // Fallback to WordPress function
                    $wp_filetype = wp_check_filetype( $new_file_path );
                    if ( $wp_filetype['type'] ) {
                        $mime_type = $wp_filetype['type'];
                    }
                }

                $attachment = array(
                    'guid'           => $new_file_path,
                    'post_mime_type' => $mime_type,
                    'post_title'     => preg_replace( '/\.[^.]+$/', '', $file_name ),
                    'post_content'   => '',
                    'post_status'    => 'inherit'
                );

                $attach_id = wp_insert_attachment( $attachment, $new_file_path );

                if ( $attach_id && !is_wp_error($attach_id) ) {
                    require_once( ABSPATH . 'wp-admin/includes/image.php' );
                    $attach_data = wp_generate_attachment_metadata( $attach_id, $new_file_path );
                    wp_update_attachment_metadata( $attach_id, $attach_data );
                    $form_data[$field_name] = wp_get_attachment_url( $attach_id );
                    error_log('LA Addons: File uploaded successfully: ' . $form_data[$field_name]);
                } else {
                    error_log('LA Addons: Failed to create attachment for uploaded file');
                }
            } catch (Exception $e) {
                error_log('LA Addons: Error processing uploaded file: ' . $e->getMessage());
            }
        }

        // Save to database
        global $wpdb;
        $table_name = $wpdb->prefix . 'la_addons_submissions';

        // Check if table exists
        if ( $wpdb->get_var("SHOW TABLES LIKE '$table_name'") != $table_name ) {
            error_log('LA Addons: Database table does not exist: ' . $table_name);
            return;
        }

        $result = $wpdb->insert(
            $table_name,
            array(
                'form_id' => $form_id,
                'submission_time' => current_time( 'mysql' ),
                'form_data' => serialize( $form_data ),
            )
        );

        if ( $result === false ) {
            error_log('LA Addons: Database insert failed: ' . $wpdb->last_error);
        } else {
            error_log('LA Addons: Submission saved successfully with ID: ' . $wpdb->insert_id);
        }

    } catch (Exception $e) {
        error_log('LA Addons: Error in save_submission: ' . $e->getMessage());
    }
}

add_action( 'wpcf7_mail_sent', 'la_addons_save_submission' );
