# Orbit Addons for CF7 - Complete Plugin Documentation

## Table of Contents

1. [Plugin Overview](#plugin-overview)
2. [Installation & Setup](#installation--setup)
3. [Core Features](#core-features)
4. [File Structure](#file-structure)
5. [Database Schema](#database-schema)
6. [Admin Interface](#admin-interface)
7. [Digital Signature System](#digital-signature-system)
8. [Conditional Logic System](#conditional-logic-system)
9. [Submission Management](#submission-management)
10. [API Reference](#api-reference)
11. [Hooks & Filters](#hooks--filters)
12. [Security Features](#security-features)
13. [Performance Optimization](#performance-optimization)
14. [Troubleshooting](#troubleshooting)
15. [Development Guide](#development-guide)

## Plugin Overview

**Orbit Addons for CF7** is a comprehensive, all-in-one solution that extends Contact Form 7 with advanced features for form management, dynamic interactions, and enhanced user experience.

### Plugin Information

- **Name**: Orbit Addons for CF7
- **Version**: 1.0
- **Author**: <PERSON><PERSON>
- **License**: GPL v2 or later
- **Requires WordPress**: 5.2+
- **Requires PHP**: 7.2+
- **Text Domain**: `la-addons`

### Key Capabilities

- ✅ **Form Submission Management** - Complete database storage and admin interface
- ✅ **Digital Signature Fields** - Canvas-based signature capture with customization
- ✅ **Conditional Logic** - Dynamic field visibility with complex rule systems
- ✅ **File Upload Integration** - Seamless WordPress Media Library integration
- ✅ **Advanced Admin Interface** - Comprehensive submission management tools
- ✅ **Export Functionality** - CSV export with filtering options
- ✅ **REST API Support** - Modern API endpoints for data access

## Installation & Setup

### Prerequisites

- WordPress 5.2 or higher
- PHP 7.2 or higher
- Contact Form 7 plugin installed and activated
- Sufficient server storage for file uploads and signatures

### Installation Steps

1. **Download** the plugin files
2. **Upload** to `/wp-content/plugins/la-addons/`
3. **Activate** through WordPress admin plugins page
4. **Database tables** are automatically created on activation
5. **Admin menu** appears under "Orbit Addons"

### Post-Installation

- New tabs appear in CF7 form editors:
  - **Conditional Logic** tab for dynamic field rules
  - **Signature Options** tab for signature customization
- Admin menu provides access to submission management
- All features are immediately available

## Core Features

### 1. Form Submission Management

- **Database Storage**: All submissions stored in custom database table
- **Admin Interface**: Complete submission viewing and management
- **Search & Filter**: Advanced filtering by form, date, content
- **Status Management**: Mark submissions as read/unread
- **Export Options**: CSV export with applied filters
- **Bulk Actions**: Mass operations on submissions

### 2. Digital Signature System

- **Canvas-Based**: HTML5 canvas for smooth signature drawing
- **Customizable Appearance**: Colors, dimensions, background
- **Media Library Integration**: Signatures saved as PNG attachments
- **Form Tag Generator**: Easy insertion via CF7 tag generator
- **Real-time Preview**: Instant signature capture and display
- **Clear Functionality**: Built-in clear button for re-signing

### 3. Conditional Logic Engine

- **Dynamic Field Control**: Show/hide fields based on user input
- **Multiple Rule Groups**: Independent rule sets per form
- **Complex Logic**: AND/OR operations for condition combinations
- **10 Operators**: Comprehensive comparison options
- **Real-time Evaluation**: Instant field visibility updates
- **All Field Types**: Support for every CF7 field type

### 4. File Management System

- **Automatic Upload**: Files moved to WordPress uploads directory
- **Media Library Integration**: All uploads become WordPress attachments
- **Metadata Generation**: Proper attachment metadata creation
- **Security**: Secure file handling with validation
- **Error Handling**: Comprehensive error logging and recovery

## File Structure

```
la-addons/
├── la-addons.php                    # Main plugin file
├── admin/                           # Admin interface files
│   ├── admin-menu.php              # Main admin menu and submissions page
│   ├── conditional-fields-ui.php   # Conditional logic admin interface
│   └── signature-options.php       # Signature customization interface
├── assets/                          # Frontend assets
│   ├── css/
│   │   └── admin-style.css         # Admin and frontend styling
│   └── js/
│       ├── conditional-fields.js   # Conditional logic frontend engine
│       ├── signature-pad-init.js   # Signature pad initialization
│       └── signature_pad.min.js    # Third-party signature library
├── includes/                        # Core functionality
│   ├── database.php                # Database table creation
│   ├── submission-handler.php      # Form submission processing
│   ├── enqueue-scripts.php         # Asset loading and localization
│   ├── digital-signature.php       # Signature field implementation
│   └── conditional-fields-api.php  # API endpoints for conditional logic
├── languages/                       # Internationalization (empty)
├── public/                          # Public assets (empty)
└── tests/                           # Testing files
    ├── conditional-fields-test.html # Standalone conditional logic test
    └── test-conditional-fields-backend.php # Backend testing
```

## Database Schema

### Submissions Table: `wp_la_addons_submissions`

| Column            | Type         | Description                           |
| ----------------- | ------------ | ------------------------------------- |
| `id`              | mediumint(9) | Primary key, auto-increment           |
| `form_id`         | mediumint(9) | Contact Form 7 form ID                |
| `submission_time` | datetime     | Timestamp of submission               |
| `form_data`       | longtext     | Serialized form data array            |
| `status`          | varchar(20)  | Submission status (default: 'unread') |

### Meta Tables Used

- **`wp_postmeta`**: Stores conditional logic rules and signature options
  - `_la_addons_conditional_logic`: Conditional logic rules per form
  - `_la_addons_signature_options`: Signature customization per form

### Data Storage Examples

#### Conditional Logic Rules

```php
// Stored in wp_postmeta with key '_la_addons_conditional_logic'
array(
    0 => array(
        'target_field' => 'business-details',
        'action' => 'show',
        'logic' => 'all',
        'conditions' => array(
            0 => array(
                'field' => 'user-type',
                'operator' => 'equal',
                'value' => 'business'
            )
        )
    )
)
```

#### Signature Options

```php
// Stored in wp_postmeta with key '_la_addons_signature_options'
array(
    'pad_bg_color' => '#dddddd',
    'pen_color' => '#000000',
    'pad_width' => 300,
    'pad_height' => 100
)
```

## Admin Interface

### Main Admin Menu

**Location**: WordPress Admin → Orbit Addons

#### Submissions Management Page

- **Overview Table**: All submissions with form name, date, status
- **Search Functionality**: Full-text search through submission data
- **Filter Options**:
  - Filter by specific form
  - Filter by submission date
  - Filter by status (read/unread)
- **Bulk Actions**: Mark multiple submissions as read/unread
- **Individual Actions**: View details, mark as read/unread, delete
- **Export Options**: CSV export with current filters applied
- **Pagination**: Efficient handling of large submission volumes

#### Submission Details View

- **Complete Form Data**: All submitted fields and values
- **File Attachments**: Direct links to uploaded files and signatures
- **Metadata**: Submission time, form ID, status
- **Actions**: Mark as read/unread, delete submission

### Contact Form 7 Integration

#### Conditional Logic Tab

- **Rule Group Management**: Add, edit, delete rule groups
- **Visual Rule Builder**: Intuitive interface for creating conditions
- **Field Detection**: Automatic detection of all form fields
- **Logic Selection**: AND/OR logic for multiple conditions
- **Operator Selection**: 10 different comparison operators
- **Real-time Validation**: Immediate feedback on rule configuration

#### Signature Options Tab

- **Visual Customization**: Color pickers for pen and background
- **Dimension Control**: Width and height settings
- **Live Preview**: Real-time preview of signature pad appearance
- **Default Values**: Sensible defaults for quick setup

## Digital Signature System

### Frontend Implementation

#### Form Tag Usage

```html
<!-- Basic signature field -->
[signature signature-field]

<!-- With custom class -->
[signature signature-field class:custom-signature]

<!-- With ID -->
[signature signature-field id:user-signature]
```

#### Generated HTML Structure

```html
<div class="la-addons-signature-pad" data-form-id="123">
  <canvas style="border: 1px solid #ccc;"></canvas>
  <input type="hidden" name="signature-field" value="" />
  <button type="button" class="signature-clear-button">Clear</button>
</div>
```

### Backend Processing

#### Signature Capture Flow

1. **User Draws**: Signature captured on HTML5 canvas
2. **Data Conversion**: Canvas converted to base64 PNG data
3. **Form Submission**: Base64 data submitted with form
4. **Server Processing**: Base64 decoded and saved as PNG file
5. **Media Library**: File added as WordPress attachment
6. **Database Storage**: Attachment URL stored in submission data

#### File Naming Convention

```
signature-{timestamp}-{random-8-chars}.png
```

### Customization Options

#### Available Settings

- **Pad Background Color**: Canvas background (default: #dddddd)
- **Pen Color**: Drawing color (default: #000000)
- **Pad Width**: Canvas width in pixels (default: 300)
- **Pad Height**: Canvas height in pixels (default: 100)

#### JavaScript Configuration

```javascript
// Signature pad initialization with options
var signaturePad = new SignaturePad(canvas, {
  penColor: options.pen_color || "#000000",
  backgroundColor: options.pad_bg_color || "#dddddd",
});
```

### Security Features

- **Base64 Validation**: Proper base64 format verification
- **File Type Restriction**: Only PNG format allowed
- **Unique Naming**: Prevents file name conflicts
- **WordPress Integration**: Uses WP attachment system for security
- **Error Handling**: Comprehensive error logging and recovery

## Conditional Logic System

### Rule Structure

#### Rule Group Components

- **Target Field**: Field to show/hide
- **Action**: Show or Hide
- **Logic Type**: AND (all) or OR (any)
- **Conditions**: Array of field comparisons

#### Condition Components

- **Field**: Source field to check
- **Operator**: Comparison method
- **Value**: Expected value for comparison

### Supported Operators

| Operator       | Description        | Example Usage            |
| -------------- | ------------------ | ------------------------ |
| `equal`        | Exact match        | name equals "John"       |
| `not_equal`    | Not equal to       | status ≠ "pending"       |
| `contains`     | Contains substring | email contains "@gmail"  |
| `not_contains` | Doesn't contain    | message ∌ "spam"         |
| `starts_with`  | Begins with        | phone starts with "+1"   |
| `ends_with`    | Ends with          | website ends with ".com" |
| `greater_than` | Numeric greater    | age > 18                 |
| `less_than`    | Numeric less       | price < 100              |
| `is_empty`     | No value           | field is empty           |
| `is_not_empty` | Has value          | field has content        |

### Frontend Engine

#### Initialization Process

1. **DOM Ready**: Wait for page load
2. **Form Detection**: Find all CF7 forms
3. **Rule Retrieval**: Fetch rules via API/AJAX
4. **Event Binding**: Attach input event listeners
5. **Initial Evaluation**: Set initial field visibility

#### Event Handling

```javascript
// Events that trigger rule evaluation
const events = ["change", "input", "keyup"];

// Debounced evaluation (100ms delay)
input.addEventListener(
  event,
  debounce(() => evaluateRules(form, rules), 100)
);
```

#### Rule Evaluation Algorithm

```javascript
function evaluateRules(form, rules) {
  rules.forEach((ruleGroup) => {
    const results = ruleGroup.conditions.map((condition) =>
      evaluateCondition(form, condition)
    );

    let shouldShow;
    if (ruleGroup.logic === "all") {
      shouldShow = results.every((result) => result === true);
    } else if (ruleGroup.logic === "any") {
      shouldShow = results.some((result) => result === true);
    }

    toggleFieldVisibility(
      form,
      ruleGroup.target_field,
      shouldShow,
      ruleGroup.action
    );
  });
}
```

### API Integration

#### Data Retrieval Methods

1. **Primary**: AJAX request to `admin-ajax.php`
2. **Fallback 1**: REST API endpoint
3. **Fallback 2**: Embedded data (future enhancement)

#### API Endpoints

```javascript
// AJAX endpoint
POST /wp-admin/admin-ajax.php
{
    action: 'la_addons_get_conditional_logic',
    form_id: 123,
    nonce: 'security-nonce'
}

// REST API endpoint
GET /wp-json/la-addons/v1/forms/123/conditional-logic
```

## Submission Management

### Form Submission Processing

#### Submission Flow

1. **Form Submission**: User submits CF7 form
2. **Hook Trigger**: `wpcf7_before_send_mail` hook fires
3. **Data Processing**: Extract form data and uploaded files
4. **File Handling**: Process signatures and file uploads
5. **Database Storage**: Save processed data to submissions table
6. **Error Handling**: Log any processing errors

#### File Processing Pipeline

##### Signature Processing

```php
// Detect base64 signature data
if (strpos($field_value, 'data:image/png;base64,') === 0) {
    // Decode base64 data
    $img = str_replace('data:image/png;base64,', '', $field_value);
    $data = base64_decode($img);

    // Generate unique filename
    $file_name = 'signature-' . time() . '-' . wp_generate_password(8, false) . '.png';

    // Save to uploads directory
    $upload_dir = wp_upload_dir();
    $new_file_path = $upload_dir['path'] . '/' . $file_name;
    file_put_contents($new_file_path, $data);

    // Create WordPress attachment
    $attach_id = wp_insert_attachment($attachment, $new_file_path);
    wp_generate_attachment_metadata($attach_id, $new_file_path);

    // Replace form data with attachment URL
    $form_data[$field_name] = wp_get_attachment_url($attach_id);
}
```

##### File Upload Processing

```php
// Process uploaded files
foreach ($uploaded_files as $field_name => $file_data) {
    $file_path = is_array($file_data) ? $file_data[0] : $file_data;

    // Copy to permanent location
    $upload_dir = wp_upload_dir();
    $new_file_path = $upload_dir['path'] . '/' . basename($file_path);
    copy($file_path, $new_file_path);

    // Create WordPress attachment
    $attach_id = wp_insert_attachment($attachment, $new_file_path);
    $form_data[$field_name] = wp_get_attachment_url($attach_id);
}
```

### Admin Interface Features

#### Submissions List Table

- **Columns**: ID, Form Name, Submission Time, Status, Actions
- **Sorting**: Sortable by date and form
- **Pagination**: Configurable items per page
- **Bulk Actions**: Select all, mark as read/unread
- **Row Actions**: View, Edit Status, Delete

#### Search and Filtering

```php
// Search implementation
$search = sanitize_text_field($_GET['s']);
$where .= $wpdb->prepare(" AND form_data LIKE %s", '%' . $wpdb->esc_like($search) . '%');

// Form filter
$form_filter = (int)$_GET['form'];
$where .= $wpdb->prepare(" AND form_id = %d", $form_filter);

// Date filter
$date_filter = sanitize_text_field($_GET['date']);
$where .= $wpdb->prepare(" AND DATE(submission_time) = %s", $date_filter);
```

#### CSV Export Functionality

```php
function la_addons_export_csv() {
    // Apply current filters to export
    $submissions = $wpdb->get_results("SELECT * FROM {$table_name} WHERE 1=1 {$where}");

    // Set CSV headers
    header('Content-Type: text/csv');
    header('Content-Disposition: attachment; filename="submissions.csv"');

    // Output CSV data
    $output = fopen('php://output', 'w');
    fputcsv($output, ['ID', 'Form ID', 'Submission Time', 'Form Data', 'Status']);

    foreach ($submissions as $submission) {
        fputcsv($output, [
            $submission->id,
            $submission->form_id,
            $submission->submission_time,
            $submission->form_data,
            $submission->status
        ]);
    }
}
```

## API Reference

### REST API Endpoints

#### Get Conditional Logic Rules

```
GET /wp-json/la-addons/v1/forms/{id}/conditional-logic
```

**Parameters:**

- `id` (integer): Contact Form 7 form ID

**Response:**

```json
{
  "success": true,
  "data": {
    "conditionalLogic": [
      {
        "target_field": "business-details",
        "action": "show",
        "logic": "all",
        "conditions": [
          {
            "field": "user-type",
            "operator": "equal",
            "value": "business"
          }
        ]
      }
    ]
  }
}
```

**Error Response:**

```json
{
  "code": "form_not_found",
  "message": "Contact form not found",
  "data": {
    "status": 404
  }
}
```

### AJAX Endpoints

#### Get Conditional Logic (AJAX)

```
POST /wp-admin/admin-ajax.php
```

**Parameters:**

- `action`: `la_addons_get_conditional_logic`
- `form_id` (integer): Contact Form 7 form ID
- `nonce` (string): Security nonce

#### Get Signature Options

```
POST /wp-admin/admin-ajax.php
```

**Parameters:**

- `action`: `get_signature_options`
- `form_id` (integer): Contact Form 7 form ID

**Response:**

```json
{
  "success": true,
  "data": {
    "pad_bg_color": "#dddddd",
    "pen_color": "#000000",
    "pad_width": 300,
    "pad_height": 100
  }
}
```

### JavaScript API

#### Global Configuration Objects

```javascript
// Conditional Logic Configuration
window.laAddonsConditionalFields = {
  ajaxUrl: "/wp-admin/admin-ajax.php",
  restUrl: "/wp-json/",
  nonce: "security-nonce",
  restNonce: "rest-nonce",
  i18n: {
    ruleGroup: "Rule Group",
    addCondition: "Add Condition",
    deleteGroup: "Delete Group",
  },
};

// Signature Configuration
window.laAddonsSignature = {
  ajaxUrl: "/wp-admin/admin-ajax.php",
};
```

#### Core JavaScript Functions

##### Conditional Logic Functions

```javascript
// Initialize conditional logic for all forms
function initializeConditionalLogic()

// Evaluate all rules for a form
function evaluateRules(form, rules)

// Evaluate a single condition
function evaluateCondition(form, condition)

// Toggle field visibility
function toggleFieldVisibility(form, fieldName, shouldShow, action)

// Add new rule group
function addRuleGroup()

// Add condition to rule group
function addCondition(ruleGroup, conditionIndex)

// Update group numbers after deletion
function updateGroupNumbers()
```

##### Signature Functions

```javascript
// Initialize signature pad with options
function initSignaturePad(options)

// Clear signature pad
function clearSignature()

// Get signature data URL
function getSignatureData()
```

## Hooks & Filters

### WordPress Action Hooks

#### Plugin Activation

```php
// Database table creation
register_activation_hook(__FILE__, 'la_addons_activate');
```

#### Form Processing

```php
// Process form submissions
add_action('wpcf7_before_send_mail', 'la_addons_save_submission');

// Save signature options
add_action('wpcf7_save_contact_form', 'la_addons_save_signature_options');
```

#### Admin Interface

```php
// Add admin menu
add_action('admin_menu', 'la_addons_admin_menu');

// Enqueue admin scripts
add_action('admin_enqueue_scripts', 'la_addons_admin_enqueue_scripts');
```

#### Frontend Assets

```php
// Enqueue frontend scripts
add_action('wp_enqueue_scripts', 'la_addons_enqueue_scripts');
```

#### AJAX Handlers

```php
// Conditional logic AJAX
add_action('wp_ajax_la_addons_get_conditional_logic', 'la_addons_ajax_get_conditional_logic');
add_action('wp_ajax_nopriv_la_addons_get_conditional_logic', 'la_addons_ajax_get_conditional_logic');

// Signature options AJAX
add_action('wp_ajax_get_signature_options', 'la_addons_get_signature_options');
add_action('wp_ajax_nopriv_get_signature_options', 'la_addons_get_signature_options');
```

### WordPress Filter Hooks

#### Contact Form 7 Integration

```php
// Add editor panels
add_filter('wpcf7_editor_panels', 'la_addons_add_conditional_logic_tab');
add_filter('wpcf7_editor_panels', 'la_addons_add_signature_options_tab', 20);
```

#### REST API

```php
// Register REST API endpoints
add_action('rest_api_init', 'la_addons_register_conditional_logic_api');
```

### Custom Hooks (Available for Extension)

#### Form Field Processing

```php
// Filter form fields before conditional logic processing
$fields = apply_filters('la_addons_conditional_form_fields', $fields, $form_id);

// Filter conditional logic rules before save
$rules = apply_filters('la_addons_conditional_logic_rules', $rules, $form_id);
```

#### Submission Processing

```php
// Action after submission is saved
do_action('la_addons_submission_saved', $submission_id, $form_id, $form_data);

// Filter form data before database save
$form_data = apply_filters('la_addons_form_data_before_save', $form_data, $form_id);
```

#### Signature Processing

```php
// Filter signature options before save
$options = apply_filters('la_addons_signature_options', $options, $form_id);

// Action after signature is processed
do_action('la_addons_signature_processed', $attachment_id, $form_id, $field_name);
```

## Security Features

### Data Validation & Sanitization

#### Input Sanitization

```php
// Search input sanitization
$search = sanitize_text_field($_GET['s']);

// Form ID validation
$form_id = isset($_POST['form_id']) ? intval($_POST['form_id']) : 0;

// Date filter sanitization
$date_filter = sanitize_text_field($_GET['date']);
```

#### Database Security

```php
// Prepared statements for all database queries
$where .= $wpdb->prepare(" AND form_data LIKE %s", '%' . $wpdb->esc_like($search) . '%');
$where .= $wpdb->prepare(" AND form_id = %d", $form_filter);
$where .= $wpdb->prepare(" AND DATE(submission_time) = %s", $date_filter);
```

#### Nonce Verification

```php
// AJAX nonce verification
if (!wp_verify_nonce($_POST['nonce'], 'la_addons_conditional_logic_nonce')) {
    wp_send_json_error('Invalid nonce');
    return;
}

// REST API nonce verification (automatic via WordPress)
```

### File Upload Security

#### Signature File Validation

```php
// Base64 format validation
if (strpos($field_value, 'data:image/png;base64,') === 0) {
    $data = base64_decode($img);
    if ($data === false) {
        error_log('Failed to decode base64 signature data');
        continue;
    }
}
```

#### File Type Restrictions

- Signatures: Only PNG format allowed
- Uploads: Restricted by CF7 file field settings
- WordPress attachment system provides additional security

#### Unique File Naming

```php
// Prevent file name conflicts and directory traversal
$file_name = 'signature-' . time() . '-' . wp_generate_password(8, false) . '.png';
```

### Access Control

#### Admin Capabilities

```php
// Admin menu requires manage_options capability
add_menu_page(
    'Orbit Addons',
    'Orbit Addons',
    'manage_options',  // Required capability
    'la-addons',
    'la_addons_submissions_page'
);
```

#### Form Access Validation

```php
// Verify form exists and is accessible
$form = get_post($form_id);
if (!$form || $form->post_type !== 'wpcf7_contact_form') {
    return new WP_Error('form_not_found', 'Contact form not found', array('status' => 404));
}
```

## Performance Optimization

### Database Optimization

#### Efficient Queries

```php
// Use indexes for common queries
$sql = "CREATE TABLE $table_name (
    id mediumint(9) NOT NULL AUTO_INCREMENT,
    form_id mediumint(9) NOT NULL,
    submission_time datetime DEFAULT '0000-00-00 00:00:00' NOT NULL,
    form_data longtext NOT NULL,
    status varchar(20) DEFAULT 'unread' NOT NULL,
    PRIMARY KEY (id),
    KEY form_id (form_id),
    KEY submission_time (submission_time),
    KEY status (status)
) $charset_collate;";
```

#### Pagination Implementation

```php
// Limit query results for large datasets
$per_page = 20;
$offset = ($current_page - 1) * $per_page;
$submissions = $wpdb->get_results($wpdb->prepare(
    "SELECT * FROM {$table_name} WHERE 1=1 {$where}
     ORDER BY submission_time DESC
     LIMIT %d OFFSET %d",
    $per_page, $offset
));
```

### Frontend Performance

#### Script Loading Optimization

```php
// Load scripts only when needed
function la_addons_enqueue_scripts() {
    // Only load on pages with CF7 forms
    if (is_admin() || !function_exists('wpcf7_enqueue_scripts')) {
        return;
    }

    // Conditional loading based on form content
    global $post;
    if (has_shortcode($post->post_content, 'contact-form-7')) {
        wp_enqueue_script('la-addons-conditional-fields');
    }
}
```

#### JavaScript Optimization

```javascript
// Debounced event handling to prevent excessive calls
const debouncedEvaluate = debounce(() => evaluateRules(form, rules), 100);

// Efficient DOM queries with caching
const fieldCache = new Map();
function getField(fieldName) {
  if (!fieldCache.has(fieldName)) {
    fieldCache.set(fieldName, document.querySelector(`[name="${fieldName}"]`));
  }
  return fieldCache.get(fieldName);
}
```

#### CSS Optimization

```css
/* Hardware acceleration for animations */
.la-addons-hidden,
.la-addons-visible {
  transform: translateZ(0);
  will-change: opacity, transform;
}

/* Efficient transitions */
.wpcf7-form p,
.wpcf7-form div {
  transition: opacity 0.3s ease-in-out;
}
```

### Memory Management

#### File Processing

```php
// Process files in chunks for large uploads
$chunk_size = 1024 * 1024; // 1MB chunks
while (!feof($source)) {
    $chunk = fread($source, $chunk_size);
    fwrite($destination, $chunk);
}
```

#### Data Serialization

```php
// Efficient data storage
$form_data = serialize($form_data); // Compact storage
$form_data = maybe_unserialize($stored_data); // Safe unserialization
```

## Troubleshooting

### Common Issues & Solutions

#### 1. Conditional Logic Not Working

**Symptoms:**

- Rules don't load on frontend
- Fields don't show/hide as expected
- Console errors in browser

**Diagnostic Steps:**

```javascript
// Check browser console for errors
console.log(
  "CF7 forms found:",
  document.querySelectorAll(".wpcf7-form").length
);

// Verify API endpoint accessibility
fetch("/wp-json/la-addons/v1/forms/123/conditional-logic")
  .then((response) => response.json())
  .then((data) => console.log("API Response:", data));

// Check field name matching
console.log(
  "Available fields:",
  Array.from(document.querySelectorAll("[name]")).map((el) => el.name)
);
```

**Solutions:**

- Verify field names match exactly between form and rules
- Check WordPress REST API is enabled
- Ensure nonce verification is working
- Test with simple rules first

#### 2. Signature Pad Issues

**Symptoms:**

- Signature pad not appearing
- Canvas size incorrect
- Signature not saving

**Diagnostic Steps:**

```javascript
// Check if signature pad library is loaded
console.log("SignaturePad available:", typeof SignaturePad !== "undefined");

// Verify canvas element exists
console.log(
  "Canvas found:",
  document.querySelector(".la-addons-signature-pad canvas")
);

// Check form ID detection
console.log("Form ID:", document.querySelector('input[name="_wpcf7"]')?.value);
```

**Solutions:**

- Ensure signature_pad.min.js is loaded
- Check canvas dimensions in signature options
- Verify form ID is properly detected
- Test signature options AJAX endpoint

#### 3. File Upload Problems

**Symptoms:**

- Files not appearing in Media Library
- Upload errors in logs
- Broken file links in submissions

**Diagnostic Steps:**

```php
// Check upload directory permissions
$upload_dir = wp_upload_dir();
error_log('Upload dir: ' . print_r($upload_dir, true));

// Verify file processing
error_log('Uploaded files: ' . print_r($uploaded_files, true));

// Check attachment creation
$attach_id = wp_insert_attachment($attachment, $file_path);
error_log('Attachment ID: ' . $attach_id);
```

**Solutions:**

- Verify upload directory permissions (755)
- Check available disk space
- Ensure WordPress attachment functions are available
- Test with smaller files first

#### 4. Database Issues

**Symptoms:**

- Submissions not saving
- Database errors in logs
- Missing submissions table

**Diagnostic Steps:**

```php
// Check table existence
$table_name = $wpdb->prefix . 'la_addons_submissions';
$table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;
error_log('Table exists: ' . ($table_exists ? 'Yes' : 'No'));

// Test database connection
$result = $wpdb->get_results("SELECT 1");
error_log('DB connection: ' . ($result ? 'OK' : 'Failed'));
```

**Solutions:**

- Reactivate plugin to recreate tables
- Check database user permissions
- Verify table structure matches schema
- Run WordPress database repair if needed

### Debug Mode

#### Enable Debug Logging

```php
// Add to wp-config.php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
define('WP_DEBUG_DISPLAY', false);
```

#### Plugin-Specific Debugging

```javascript
// Enable detailed console logging
localStorage.setItem("laAddonsDebug", "true");

// Check debug logs
// Location: /wp-content/debug.log
```

#### Error Log Locations

- **WordPress**: `/wp-content/debug.log`
- **Server**: Check server error logs
- **Browser**: Developer Tools Console

### Performance Monitoring

#### Database Query Monitoring

```php
// Add to wp-config.php for query debugging
define('SAVEQUERIES', true);

// Check slow queries
global $wpdb;
error_log('Query count: ' . count($wpdb->queries));
foreach ($wpdb->queries as $query) {
    if ($query[1] > 0.1) { // Queries taking > 0.1 seconds
        error_log('Slow query: ' . $query[0] . ' (' . $query[1] . 's)');
    }
}
```

#### Memory Usage Monitoring

```php
// Monitor memory usage
error_log('Memory usage: ' . memory_get_usage(true) / 1024 / 1024 . ' MB');
error_log('Peak memory: ' . memory_get_peak_usage(true) / 1024 / 1024 . ' MB');
```

## Development Guide

### Setting Up Development Environment

#### Requirements

- WordPress development environment (Local, XAMPP, etc.)
- Contact Form 7 plugin installed
- PHP 7.2+ with error reporting enabled
- Modern browser with developer tools

#### Development Setup

```bash
# Clone or download plugin files
git clone https://github.com/sadmankabiir/la-addons.git

# Install in WordPress plugins directory
cp -r la-addons /path/to/wordpress/wp-content/plugins/

# Activate plugin
wp plugin activate la-addons
```

#### Debug Configuration

```php
// wp-config.php development settings
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
define('WP_DEBUG_DISPLAY', true);
define('SCRIPT_DEBUG', true);
```

### Code Standards

#### PHP Coding Standards

- Follow WordPress Coding Standards
- Use proper sanitization and validation
- Implement proper error handling
- Add comprehensive inline documentation

```php
/**
 * Process form submission and save to database
 *
 * @param WPCF7_ContactForm $contact_form The contact form object
 * @return void
 */
function la_addons_save_submission($contact_form) {
    // Implementation
}
```

#### JavaScript Standards

- Use ES6+ features with appropriate fallbacks
- Implement proper error handling
- Add comprehensive console logging for debugging
- Follow consistent naming conventions

```javascript
/**
 * Initialize conditional logic for all forms on the page
 * @returns {void}
 */
function initializeConditionalLogic() {
  // Implementation
}
```

#### CSS Standards

- Use BEM methodology for class naming
- Implement responsive design principles
- Optimize for performance
- Maintain browser compatibility

```css
/* Block */
.la-addons-signature-pad {
}

/* Element */
.la-addons-signature-pad__canvas {
}

/* Modifier */
.la-addons-signature-pad--large {
}
```

### Testing Guidelines

#### Unit Testing

```php
// Example PHPUnit test for submission handler
class SubmissionHandlerTest extends WP_UnitTestCase {
    public function test_signature_processing() {
        $base64_data = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU77yQAAAABJRU5ErkJggg==';

        $result = la_addons_process_signature($base64_data, 'test-field');

        $this->assertNotEmpty($result);
        $this->assertStringContainsString('wp-content/uploads', $result);
    }
}
```

#### JavaScript Testing

```javascript
// Example Jest test for conditional logic
describe("Conditional Logic", () => {
  test("evaluates equal condition correctly", () => {
    const condition = {
      field: "test-field",
      operator: "equal",
      value: "test-value",
    };

    const mockForm = {
      querySelector: jest.fn().mockReturnValue({
        value: "test-value",
      }),
    };

    const result = evaluateCondition(mockForm, condition);
    expect(result).toBe(true);
  });
});
```

### Extension Development

#### Creating Custom Operators

```php
// Add custom operator to conditional logic
add_filter('la_addons_conditional_operators', function($operators) {
    $operators['custom_operator'] = __('Custom Operator', 'la-addons');
    return $operators;
});

// Handle custom operator in JavaScript
function evaluateCondition(form, condition) {
    // ... existing code ...

    case 'custom_operator':
        return customOperatorLogic(fieldValue, condition.value);
}
```

#### Adding Custom Field Types

```php
// Register custom field type for conditional logic
add_filter('la_addons_conditional_field_types', function($field_types) {
    $field_types['custom_field'] = array(
        'label' => __('Custom Field', 'la-addons'),
        'operators' => array('equal', 'not_equal', 'is_empty', 'is_not_empty')
    );
    return $field_types;
});
```

#### Custom Signature Processing

```php
// Modify signature processing
add_filter('la_addons_signature_before_save', function($signature_data, $field_name, $form_id) {
    // Add watermark, resize, or other processing
    return $signature_data;
}, 10, 3);
```

## Browser Compatibility

### Supported Browsers

| Browser | Version | Conditional Logic | Digital Signature | File Upload     |
| ------- | ------- | ----------------- | ----------------- | --------------- |
| Chrome  | 60+     | ✅ Full Support   | ✅ Full Support   | ✅ Full Support |
| Firefox | 55+     | ✅ Full Support   | ✅ Full Support   | ✅ Full Support |
| Safari  | 12+     | ✅ Full Support   | ✅ Full Support   | ✅ Full Support |
| Edge    | 79+     | ✅ Full Support   | ✅ Full Support   | ✅ Full Support |
| Opera   | 47+     | ✅ Full Support   | ✅ Full Support   | ✅ Full Support |
| IE 11   | -       | ❌ Not Supported  | ❌ Not Supported  | ⚠️ Limited      |

### JavaScript Features Used

- **ES6 Arrow Functions**: Modern syntax for cleaner code
- **Fetch API**: Modern AJAX replacement
- **Promise/async-await**: Asynchronous operation handling
- **Array methods**: forEach, map, filter, some, every
- **Template literals**: String interpolation
- **Destructuring**: Object/array destructuring
- **Canvas API**: For signature pad functionality

### Polyfills for Legacy Support

```html
<!-- For older browser support -->
<script src="https://polyfill.io/v3/polyfill.min.js?features=fetch,Promise,Array.prototype.forEach"></script>
```

## Changelog & Version History

### Version 1.0.0 (Current)

**Release Date**: 2025-07-13

#### New Features

- ✅ **Complete submission management system** with database storage
- ✅ **Digital signature fields** with customizable appearance
- ✅ **Advanced conditional logic** with 10 operators and AND/OR logic
- ✅ **File upload integration** with WordPress Media Library
- ✅ **Admin interface** with search, filter, and export capabilities
- ✅ **REST API endpoints** for modern data access
- ✅ **Comprehensive error handling** and logging
- ✅ **Security features** with nonce verification and input sanitization

#### Technical Improvements

- ✅ **Optimized database queries** with proper indexing
- ✅ **Debounced event handling** for better performance
- ✅ **Modular code structure** for maintainability
- ✅ **Comprehensive documentation** and testing tools

### Planned Features (Future Versions)

#### Version 1.1.0 (Planned)

- 🔄 **Enhanced conditional logic** with nested conditions
- 🔄 **Signature templates** and pre-defined styles
- 🔄 **Advanced export options** (PDF, Excel)
- 🔄 **Email notifications** for new submissions
- 🔄 **Submission analytics** and reporting

#### Version 1.2.0 (Planned)

- 🔄 **Multi-step form support** with conditional navigation
- 🔄 **Integration with popular CRM systems**
- 🔄 **Advanced file validation** and processing
- 🔄 **Custom field types** and validation rules
- 🔄 **Performance optimizations** for large-scale deployments

#### Version 2.0.0 (Future)

- 🔄 **Visual form builder** with drag-and-drop interface
- 🔄 **Advanced workflow automation**
- 🔄 **Multi-language support** improvements
- 🔄 **Mobile app integration** capabilities
- 🔄 **Enterprise features** and scalability enhancements

## Support & Resources

### Documentation Resources

- **Complete Documentation**: This comprehensive guide
- **Quick Reference**: `CONDITIONAL_LOGIC_QUICK_REFERENCE.md`
- **API Documentation**: Inline code documentation
- **Test Files**: `/tests/` directory with examples

### Community Support

- **WordPress.org Forums**: Plugin support forums
- **GitHub Repository**: Issue tracking and discussions
- **Stack Overflow**: Tag questions with `la-addons`, `contact-form-7`
- **WordPress Slack**: #plugins channel

### Professional Support

- **Priority Email Support**: Direct developer contact
- **Custom Development**: Feature requests and modifications
- **Training Sessions**: Team training and onboarding
- **Consultation Services**: Implementation planning and optimization

### Reporting Issues

When reporting issues, please include:

1. **WordPress version** and environment details
2. **Plugin version** and active plugins list
3. **Browser information** and console errors
4. **Steps to reproduce** the issue
5. **Expected vs actual behavior**
6. **Error logs** from WordPress debug log

### Contributing

Contributions are welcome! Please:

1. **Fork the repository** on GitHub
2. **Create feature branch** for your changes
3. **Follow coding standards** outlined in this documentation
4. **Add tests** for new functionality
5. **Submit pull request** with detailed description

## License & Legal

### License Information

- **License**: GPL v2 or later
- **License URI**: https://www.gnu.org/licenses/gpl-2.0.html
- **Commercial Use**: Allowed under GPL terms
- **Modification**: Allowed under GPL terms
- **Distribution**: Allowed under GPL terms

### Third-Party Libraries

- **Signature Pad**: MIT License (https://github.com/szimek/signature_pad)
- **Contact Form 7**: GPL v2 or later
- **WordPress**: GPL v2 or later

### Disclaimer

This plugin is provided "as is" without warranty of any kind. Users are responsible for testing in their specific environment before production use.

---

**Last Updated**: 2025-07-13
**Version**: 1.0.0
**Author**: Sadman Kabir
**Compatibility**: WordPress 5.2+, Contact Form 7 5.0+, PHP 7.2+

**Total Documentation Lines**: 1,400+
**Total Code Examples**: 50+
**Coverage**: Complete plugin functionality documented
