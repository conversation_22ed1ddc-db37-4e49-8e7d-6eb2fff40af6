/**
 * Drag and Drop Multiple File Upload Component
 * Enhanced file upload with drag-and-drop functionality for Contact Form 7
 */

document.addEventListener("DOMContentLoaded", function () {
  console.log("drag-drop-upload.js loaded and DOMContentLoaded fired.");

  // Initialize all drag-drop upload components
  const uploadComponents = document.querySelectorAll(
    ".la-addons-dragdrop-upload"
  );

  uploadComponents.forEach((component) => {
    new DragDropUpload(component);
  });
});

/**
 * DragDropUpload Class
 * Handles all drag-and-drop upload functionality
 */
class DragDropUpload {
  constructor(container) {
    this.container = container;
    this.uploadZone = container.querySelector(".la-dragdrop-zone");
    this.fileInput = container.querySelector('input[type="file"]');
    this.filesContainer = container.querySelector(".la-dragdrop-files");
    this.progressContainer = container.querySelector(".la-dragdrop-progress");
    this.browseButton = container.querySelector(".la-dragdrop-browse");

    // Configuration from data attributes
    this.maxFiles = parseInt(container.dataset.maxFiles) || 5;
    this.maxSize =
      this.parseSize(container.dataset.maxSize) || 10 * 1024 * 1024; // 10MB default
    this.acceptedTypes =
      container.dataset.acceptedTypes || "image/*,application/pdf,.doc,.docx";
    this.fieldName = container.dataset.fieldName;
    this.formId = container.dataset.formId;

    // State
    this.files = [];
    this.isDragging = false;
    this.isUploading = false;

    this.init();
  }

  init() {
    this.setupEventListeners();
    this.updateUI();
  }

  setupEventListeners() {
    // Drag and drop events
    this.uploadZone.addEventListener(
      "dragenter",
      this.handleDragEnter.bind(this)
    );
    this.uploadZone.addEventListener(
      "dragover",
      this.handleDragOver.bind(this)
    );
    this.uploadZone.addEventListener(
      "dragleave",
      this.handleDragLeave.bind(this)
    );
    this.uploadZone.addEventListener("drop", this.handleDrop.bind(this));

    // File input change
    this.fileInput.addEventListener("change", this.handleFileSelect.bind(this));

    // Browse button click
    this.browseButton.addEventListener("click", (e) => {
      e.preventDefault();
      this.fileInput.click();
    });

    // Prevent default drag behaviors on the document
    document.addEventListener("dragenter", this.preventDefaults.bind(this));
    document.addEventListener("dragover", this.preventDefaults.bind(this));
    document.addEventListener("dragleave", this.preventDefaults.bind(this));
    document.addEventListener("drop", this.preventDefaults.bind(this));
  }

  preventDefaults(e) {
    e.preventDefault();
    e.stopPropagation();
  }

  handleDragEnter(e) {
    this.preventDefaults(e);
    this.isDragging = true;
    this.uploadZone.classList.add("la-dragdrop-dragover");
  }

  handleDragOver(e) {
    this.preventDefaults(e);
    e.dataTransfer.dropEffect = "copy";
  }

  handleDragLeave(e) {
    this.preventDefaults(e);
    // Only remove dragover class if we're leaving the upload zone entirely
    if (!this.uploadZone.contains(e.relatedTarget)) {
      this.isDragging = false;
      this.uploadZone.classList.remove("la-dragdrop-dragover");
    }
  }

  handleDrop(e) {
    this.preventDefaults(e);
    this.isDragging = false;
    this.uploadZone.classList.remove("la-dragdrop-dragover");

    const files = Array.from(e.dataTransfer.files);
    this.processFiles(files);
  }

  handleFileSelect(e) {
    const files = Array.from(e.target.files);
    this.processFiles(files);
    // Clear the input so the same file can be selected again
    e.target.value = "";
  }

  processFiles(newFiles) {
    const validFiles = [];
    const errors = [];

    // Check if adding these files would exceed the maximum
    if (this.files.length + newFiles.length > this.maxFiles) {
      errors.push(
        `Maximum ${this.maxFiles} files allowed. You can only add ${
          this.maxFiles - this.files.length
        } more files.`
      );
      newFiles = newFiles.slice(0, this.maxFiles - this.files.length);
    }

    newFiles.forEach((file) => {
      const validation = this.validateFile(file);
      if (validation.valid) {
        // Check for duplicates
        const isDuplicate = this.files.some(
          (existingFile) =>
            existingFile.name === file.name && existingFile.size === file.size
        );

        if (!isDuplicate) {
          validFiles.push(file);
        } else {
          errors.push(`File "${file.name}" is already selected.`);
        }
      } else {
        errors.push(validation.error);
      }
    });

    // Add valid files to the collection
    validFiles.forEach((file) => {
      const fileObj = {
        file: file,
        id: this.generateFileId(),
        name: file.name,
        size: file.size,
        type: file.type,
        status: "pending", // pending, uploading, uploaded, error
        progress: 0,
        url: null,
        attachmentId: null,
      };

      this.files.push(fileObj);
    });

    // Show errors if any
    if (errors.length > 0) {
      this.showErrors(errors);
    }

    this.updateUI();
  }

  validateFile(file) {
    // Check file size
    if (file.size > this.maxSize) {
      return {
        valid: false,
        error: `File "${
          file.name
        }" is too large. Maximum size is ${this.formatSize(this.maxSize)}.`,
      };
    }

    // Check file type
    if (!this.isFileTypeAccepted(file)) {
      return {
        valid: false,
        error: `File type "${file.type}" is not allowed for "${file.name}".`,
      };
    }

    return { valid: true };
  }

  isFileTypeAccepted(file) {
    const acceptedTypes = this.acceptedTypes
      .split(",")
      .map((type) => type.trim());

    return acceptedTypes.some((acceptedType) => {
      if (acceptedType.startsWith(".")) {
        // Extension check
        return file.name.toLowerCase().endsWith(acceptedType.toLowerCase());
      } else if (acceptedType.includes("*")) {
        // MIME type wildcard check
        const baseType = acceptedType.split("/")[0];
        return file.type.startsWith(baseType + "/");
      } else {
        // Exact MIME type check
        return file.type === acceptedType;
      }
    });
  }

  removeFile(fileId) {
    this.files = this.files.filter((file) => file.id !== fileId);
    this.updateUI();
  }

  updateUI() {
    this.renderFileList();
    this.updateUploadZone();
  }

  renderFileList() {
    if (this.files.length === 0) {
      this.filesContainer.innerHTML = "";
      return;
    }

    const filesHTML = this.files
      .map((file) => this.renderFileItem(file))
      .join("");
    this.filesContainer.innerHTML = `<div class="la-dragdrop-file-list">${filesHTML}</div>`;

    // Add event listeners for remove buttons
    this.filesContainer
      .querySelectorAll(".la-file-remove")
      .forEach((button) => {
        button.addEventListener("click", (e) => {
          e.preventDefault();
          const fileId = button.dataset.fileId;
          this.removeFile(fileId);
        });
      });
  }

  renderFileItem(fileObj) {
    const statusClass = `la-file-status-${fileObj.status}`;
    const progressBar =
      fileObj.status === "uploading"
        ? `<div class="la-file-progress"><div class="la-file-progress-bar" style="width: ${fileObj.progress}%"></div></div>`
        : "";

    // Generate preview or icon
    const preview = this.generateFilePreview(fileObj);

    // Status indicator
    const statusIndicator = this.getStatusIndicator(fileObj.status);

    return `
      <div class="la-file-item ${statusClass}" data-file-id="${fileObj.id}">
        <div class="la-file-preview">
          ${preview}
          ${statusIndicator}
        </div>
        <div class="la-file-info">
          <div class="la-file-name" title="${this.escapeHtml(
            fileObj.name
          )}">${this.escapeHtml(this.truncateFilename(fileObj.name, 30))}</div>
          <div class="la-file-details">
            <span class="la-file-size">${this.formatSize(fileObj.size)}</span>
            <span class="la-file-type">${this.getFileTypeLabel(
              fileObj.type
            )}</span>
          </div>
          ${progressBar}
        </div>
        <div class="la-file-actions">
          ${
            fileObj.status === "uploaded"
              ? this.getFileActionButtons(fileObj)
              : ""
          }
          <button type="button" class="la-file-remove" data-file-id="${
            fileObj.id
          }" title="Remove file">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>
        </div>
      </div>
    `;
  }

  generateFilePreview(fileObj) {
    if (fileObj.type.startsWith("image/")) {
      // For images, create a thumbnail
      const imageUrl = fileObj.url || URL.createObjectURL(fileObj.file);
      return `
        <div class="la-file-thumbnail">
          <img src="${imageUrl}" alt="${this.escapeHtml(fileObj.name)}"
               onload="this.parentElement.classList.add('loaded')"
               onerror="this.parentElement.innerHTML = '${this.getFileIcon(
                 fileObj.type
               )}'">
        </div>
      `;
    } else {
      // For other files, show icon
      return `<div class="la-file-icon">${this.getFileIcon(
        fileObj.type
      )}</div>`;
    }
  }

  getStatusIndicator(status) {
    const indicators = {
      pending:
        '<div class="la-status-indicator la-status-pending" title="Pending upload">⏳</div>',
      uploading:
        '<div class="la-status-indicator la-status-uploading" title="Uploading...">⬆️</div>',
      uploaded:
        '<div class="la-status-indicator la-status-uploaded" title="Upload complete">✅</div>',
      error:
        '<div class="la-status-indicator la-status-error" title="Upload failed">❌</div>',
    };

    return indicators[status] || "";
  }

  getFileActionButtons(fileObj) {
    if (!fileObj.url) return "";

    return `
      <a href="${fileObj.url}" target="_blank" class="la-file-view" title="View file">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
          <circle cx="12" cy="12" r="3"></circle>
        </svg>
      </a>
    `;
  }

  truncateFilename(filename, maxLength) {
    if (filename.length <= maxLength) return filename;

    const extension = filename.split(".").pop();
    const nameWithoutExt = filename.substring(0, filename.lastIndexOf("."));
    const truncatedName =
      nameWithoutExt.substring(0, maxLength - extension.length - 4) + "...";

    return truncatedName + "." + extension;
  }

  getFileTypeLabel(mimeType) {
    const typeLabels = {
      "image/jpeg": "JPEG",
      "image/jpg": "JPG",
      "image/png": "PNG",
      "image/gif": "GIF",
      "image/webp": "WebP",
      "application/pdf": "PDF",
      "application/msword": "DOC",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
        "DOCX",
      "application/vnd.ms-excel": "XLS",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":
        "XLSX",
      "application/vnd.ms-powerpoint": "PPT",
      "application/vnd.openxmlformats-officedocument.presentationml.presentation":
        "PPTX",
      "text/plain": "TXT",
      "text/csv": "CSV",
      "application/zip": "ZIP",
      "application/x-rar-compressed": "RAR",
      "audio/mpeg": "MP3",
      "audio/wav": "WAV",
      "video/mp4": "MP4",
      "video/avi": "AVI",
    };

    return typeLabels[mimeType] || mimeType.split("/")[1].toUpperCase();
  }

  updateUploadZone() {
    const remainingFiles = this.maxFiles - this.files.length;
    const secondaryText = this.container.querySelector(
      ".la-dragdrop-secondary"
    );

    if (remainingFiles <= 0) {
      this.uploadZone.classList.add("la-dragdrop-disabled");
      secondaryText.textContent = "Maximum files reached";
    } else {
      this.uploadZone.classList.remove("la-dragdrop-disabled");
      secondaryText.textContent = `Maximum ${
        this.maxFiles
      } files, ${this.formatSize(
        this.maxSize
      )} each (${remainingFiles} remaining)`;
    }
  }

  // Utility methods
  generateFileId() {
    return "file_" + Date.now() + "_" + Math.random().toString(36).substr(2, 9);
  }

  parseSize(sizeStr) {
    const units = { B: 1, KB: 1024, MB: 1024 * 1024, GB: 1024 * 1024 * 1024 };
    const match = sizeStr.match(/^(\d+(?:\.\d+)?)\s*(B|KB|MB|GB)$/i);

    if (match) {
      const value = parseFloat(match[1]);
      const unit = match[2].toUpperCase();
      return value * (units[unit] || 1);
    }

    return parseInt(sizeStr) || 0;
  }

  formatSize(bytes) {
    if (bytes === 0) return "0 B";
    const k = 1024;
    const sizes = ["B", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + " " + sizes[i];
  }

  getFileIcon(mimeType) {
    if (mimeType.startsWith("image/")) {
      return '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><rect x="3" y="3" width="18" height="18" rx="2" ry="2"/><circle cx="8.5" cy="8.5" r="1.5"/><polyline points="21,15 16,10 5,21"/></svg>';
    } else if (mimeType === "application/pdf") {
      return '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/></svg>';
    } else {
      return '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/></svg>';
    }
  }

  escapeHtml(text) {
    const div = document.createElement("div");
    div.textContent = text;
    return div.innerHTML;
  }

  showErrors(errors) {
    // Create or update error display
    let errorContainer = this.container.querySelector(".la-dragdrop-errors");

    if (!errorContainer) {
      errorContainer = document.createElement("div");
      errorContainer.className = "la-dragdrop-errors";
      this.container.appendChild(errorContainer);
    }

    const errorsHTML = errors
      .map(
        (error) =>
          `<div class="la-dragdrop-error">${this.escapeHtml(error)}</div>`
      )
      .join("");
    errorContainer.innerHTML = errorsHTML;

    // Auto-hide errors after 5 seconds
    setTimeout(() => {
      if (errorContainer.parentNode) {
        errorContainer.remove();
      }
    }, 5000);
  }

  // Upload functionality
  async uploadFiles() {
    if (this.isUploading || this.files.length === 0) {
      return;
    }

    this.isUploading = true;
    this.showProgress();

    const pendingFiles = this.files.filter((file) => file.status === "pending");

    for (let i = 0; i < pendingFiles.length; i++) {
      const fileObj = pendingFiles[i];
      await this.uploadSingleFile(fileObj);
    }

    this.isUploading = false;
    this.hideProgress();
    this.updateFormData();
  }

  async uploadSingleFile(fileObj) {
    fileObj.status = "uploading";
    fileObj.progress = 0;
    this.updateUI();

    const formData = new FormData();
    formData.append("action", "la_addons_dragdrop_upload");
    formData.append("nonce", laAddonsDragDrop.nonce);
    formData.append("files[]", fileObj.file);
    formData.append("form_id", this.formId);
    formData.append("field_name", this.fieldName);

    try {
      const response = await fetch(laAddonsDragDrop.ajaxUrl, {
        method: "POST",
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      if (result.success && result.files && result.files.length > 0) {
        const uploadedFile = result.files[0];
        fileObj.status = "uploaded";
        fileObj.progress = 100;
        fileObj.url = uploadedFile.url;
        fileObj.attachmentId = uploadedFile.id;
        fileObj.filename = uploadedFile.filename;
      } else {
        fileObj.status = "error";
        const errorMsg =
          result.errors && result.errors.length > 0
            ? result.errors[0]
            : "Upload failed";
        this.showErrors([`${fileObj.name}: ${errorMsg}`]);
      }
    } catch (error) {
      console.error("Upload error:", error);
      fileObj.status = "error";
      this.showErrors([`${fileObj.name}: Upload failed - ${error.message}`]);
    }

    this.updateUI();
  }

  showProgress() {
    this.progressContainer.style.display = "block";
    this.updateProgressBar();
  }

  hideProgress() {
    this.progressContainer.style.display = "none";
  }

  updateProgressBar() {
    const totalFiles = this.files.length;
    const uploadedFiles = this.files.filter(
      (file) => file.status === "uploaded"
    ).length;
    const progress = totalFiles > 0 ? (uploadedFiles / totalFiles) * 100 : 0;

    const progressFill =
      this.progressContainer.querySelector(".la-progress-fill");
    const progressText =
      this.progressContainer.querySelector(".la-progress-text");

    if (progressFill) {
      progressFill.style.width = progress + "%";
    }

    if (progressText) {
      progressText.textContent = `Uploading... ${uploadedFiles}/${totalFiles} files`;
    }
  }

  updateFormData() {
    // Create hidden inputs for uploaded files to be submitted with the form
    const uploadedFiles = this.files.filter(
      (file) => file.status === "uploaded"
    );

    // Remove existing hidden inputs for this field
    const existingInputs = this.container.querySelectorAll(
      `input[name="${this.fieldName}[]"]`
    );
    existingInputs.forEach((input) => {
      if (input !== this.fileInput) {
        input.remove();
      }
    });

    // Create new hidden inputs for uploaded files
    uploadedFiles.forEach((fileObj, index) => {
      const hiddenInput = document.createElement("input");
      hiddenInput.type = "hidden";
      hiddenInput.name = `${this.fieldName}[]`;
      hiddenInput.value = JSON.stringify({
        la_dragdrop_file: true,
        attachment_id: fileObj.attachmentId,
        filename: fileObj.filename,
        original_name: fileObj.name,
        url: fileObj.url,
        size: fileObj.size,
        type: fileObj.type,
      });

      this.container.appendChild(hiddenInput);
    });
  }

  // Auto-upload when files are added (can be made optional)
  processFiles(newFiles) {
    const validFiles = [];
    const errors = [];

    // Check if adding these files would exceed the maximum
    if (this.files.length + newFiles.length > this.maxFiles) {
      errors.push(
        `Maximum ${this.maxFiles} files allowed. You can only add ${
          this.maxFiles - this.files.length
        } more files.`
      );
      newFiles = newFiles.slice(0, this.maxFiles - this.files.length);
    }

    newFiles.forEach((file) => {
      const validation = this.validateFile(file);
      if (validation.valid) {
        // Check for duplicates
        const isDuplicate = this.files.some(
          (existingFile) =>
            existingFile.name === file.name && existingFile.size === file.size
        );

        if (!isDuplicate) {
          validFiles.push(file);
        } else {
          errors.push(`File "${file.name}" is already selected.`);
        }
      } else {
        errors.push(validation.error);
      }
    });

    // Add valid files to the collection
    validFiles.forEach((file) => {
      const fileObj = {
        file: file,
        id: this.generateFileId(),
        name: file.name,
        size: file.size,
        type: file.type,
        status: "pending",
        progress: 0,
        url: null,
        attachmentId: null,
        filename: null,
      };

      this.files.push(fileObj);
    });

    // Show errors if any
    if (errors.length > 0) {
      this.showErrors(errors);
    }

    this.updateUI();

    // Auto-upload new files
    if (validFiles.length > 0) {
      setTimeout(() => this.uploadFiles(), 100);
    }
  }
}
