/**
 * Drag-and-Drop Upload Tag Generator for Contact Form 7 Admin
 * Enhances the tag generator interface with dynamic functionality
 */

document.addEventListener("DOMContentLoaded", function () {
  // Only run in CF7 admin pages
  if (!document.querySelector('.contact-form-editor')) {
    return;
  }
  
  // Initialize tag generator enhancements
  initializeDragDropTagGenerator();
});

function initializeDragDropTagGenerator() {
  // Wait for CF7 tag generator to be ready
  const checkTagGenerator = setInterval(() => {
    const tagGeneratorPanel = document.querySelector('#tag-generator-panel-dragdrop-upload');
    if (tagGeneratorPanel) {
      clearInterval(checkTagGenerator);
      setupTagGeneratorEnhancements(tagGeneratorPanel);
    }
  }, 100);
}

function setupTagGeneratorEnhancements(panel) {
  // Add event listeners for dynamic tag generation
  const nameInput = panel.querySelector('input[name="name"]');
  const maxFilesInput = panel.querySelector('input[name="max-files"]');
  const maxSizeInput = panel.querySelector('input[name="max-size"]');
  const acceptInput = panel.querySelector('input[name="accept"]');
  const idInput = panel.querySelector('input[name="id"]');
  const classInput = panel.querySelector('input[name="class"]');
  const tagOutput = panel.querySelector('input.tag.code');
  
  // Update tag when inputs change
  const inputs = [nameInput, maxFilesInput, maxSizeInput, acceptInput, idInput, classInput];
  inputs.forEach(input => {
    if (input) {
      input.addEventListener('input', () => updateTagOutput(panel));
      input.addEventListener('change', () => updateTagOutput(panel));
    }
  });
  
  // Add preset buttons for common configurations
  addPresetButtons(panel);
  
  // Add file type helper
  addFileTypeHelper(panel);
  
  // Initial tag generation
  updateTagOutput(panel);
}

function updateTagOutput(panel) {
  const nameInput = panel.querySelector('input[name="name"]');
  const maxFilesInput = panel.querySelector('input[name="max-files"]');
  const maxSizeInput = panel.querySelector('input[name="max-size"]');
  const acceptInput = panel.querySelector('input[name="accept"]');
  const idInput = panel.querySelector('input[name="id"]');
  const classInput = panel.querySelector('input[name="class"]');
  const tagOutput = panel.querySelector('input.tag.code');
  
  if (!nameInput || !tagOutput) return;
  
  let tag = 'dragdrop-upload';
  let attributes = [];
  
  // Add name (required)
  if (nameInput.value.trim()) {
    attributes.push(nameInput.value.trim());
  }
  
  // Add optional attributes
  if (maxFilesInput && maxFilesInput.value && maxFilesInput.value !== '5') {
    attributes.push(`max-files:${maxFilesInput.value}`);
  }
  
  if (maxSizeInput && maxSizeInput.value && maxSizeInput.value !== '10MB') {
    attributes.push(`max-size:${maxSizeInput.value}`);
  }
  
  if (acceptInput && acceptInput.value && acceptInput.value !== 'image/*,application/pdf,.doc,.docx') {
    attributes.push(`accept:"${acceptInput.value}"`);
  }
  
  if (idInput && idInput.value.trim()) {
    attributes.push(`id:${idInput.value.trim()}`);
  }
  
  if (classInput && classInput.value.trim()) {
    attributes.push(`class:${classInput.value.trim()}`);
  }
  
  // Build final tag
  const finalTag = `[${tag} ${attributes.join(' ')}]`;
  tagOutput.value = finalTag;
}

function addPresetButtons(panel) {
  const acceptInput = panel.querySelector('input[name="accept"]');
  const maxSizeInput = panel.querySelector('input[name="max-size"]');
  const maxFilesInput = panel.querySelector('input[name="max-files"]');
  
  if (!acceptInput) return;
  
  // Create preset container
  const presetContainer = document.createElement('div');
  presetContainer.className = 'dragdrop-presets';
  presetContainer.style.marginTop = '10px';
  
  const presetLabel = document.createElement('label');
  presetLabel.textContent = 'Quick Presets:';
  presetLabel.style.fontWeight = 'bold';
  presetLabel.style.display = 'block';
  presetLabel.style.marginBottom = '5px';
  
  presetContainer.appendChild(presetLabel);
  
  // Define presets
  const presets = [
    {
      name: 'Images Only',
      accept: 'image/*',
      maxSize: '5MB',
      maxFiles: '10'
    },
    {
      name: 'Documents',
      accept: 'application/pdf,.doc,.docx,.txt,.rtf',
      maxSize: '10MB',
      maxFiles: '5'
    },
    {
      name: 'Media Files',
      accept: 'image/*,video/*,audio/*',
      maxSize: '50MB',
      maxFiles: '3'
    },
    {
      name: 'Archives',
      accept: '.zip,.rar,.7z,.tar,.gz',
      maxSize: '20MB',
      maxFiles: '2'
    }
  ];
  
  // Create preset buttons
  presets.forEach(preset => {
    const button = document.createElement('button');
    button.type = 'button';
    button.className = 'button button-secondary';
    button.textContent = preset.name;
    button.style.marginRight = '5px';
    button.style.marginBottom = '5px';
    
    button.addEventListener('click', () => {
      acceptInput.value = preset.accept;
      if (maxSizeInput) maxSizeInput.value = preset.maxSize;
      if (maxFilesInput) maxFilesInput.value = preset.maxFiles;
      updateTagOutput(panel);
    });
    
    presetContainer.appendChild(button);
  });
  
  // Insert after the accept input
  const acceptRow = acceptInput.closest('tr');
  if (acceptRow && acceptRow.nextElementSibling) {
    const newRow = document.createElement('tr');
    const newCell = document.createElement('td');
    newCell.colSpan = 2;
    newCell.appendChild(presetContainer);
    newRow.appendChild(newCell);
    
    acceptRow.parentNode.insertBefore(newRow, acceptRow.nextElementSibling);
  }
}

function addFileTypeHelper(panel) {
  const acceptInput = panel.querySelector('input[name="accept"]');
  if (!acceptInput) return;
  
  // Create helper container
  const helperContainer = document.createElement('div');
  helperContainer.className = 'dragdrop-file-type-helper';
  helperContainer.style.marginTop = '10px';
  helperContainer.style.padding = '10px';
  helperContainer.style.backgroundColor = '#f9f9f9';
  helperContainer.style.border = '1px solid #e5e5e5';
  helperContainer.style.borderRadius = '3px';
  
  const helperTitle = document.createElement('strong');
  helperTitle.textContent = 'Common File Types:';
  helperContainer.appendChild(helperTitle);
  
  const helperList = document.createElement('div');
  helperList.style.marginTop = '5px';
  helperList.style.fontSize = '12px';
  helperList.style.lineHeight = '1.4';
  
  const fileTypes = [
    { label: 'All Images', value: 'image/*' },
    { label: 'JPEG Images', value: '.jpg,.jpeg' },
    { label: 'PNG Images', value: '.png' },
    { label: 'PDF Documents', value: 'application/pdf' },
    { label: 'Word Documents', value: '.doc,.docx' },
    { label: 'Excel Files', value: '.xls,.xlsx' },
    { label: 'PowerPoint', value: '.ppt,.pptx' },
    { label: 'Text Files', value: '.txt,.rtf' },
    { label: 'Audio Files', value: 'audio/*' },
    { label: 'Video Files', value: 'video/*' },
    { label: 'ZIP Archives', value: '.zip,.rar' }
  ];
  
  fileTypes.forEach(type => {
    const typeSpan = document.createElement('span');
    typeSpan.innerHTML = `<code>${type.value}</code> - ${type.label}`;
    typeSpan.style.display = 'block';
    typeSpan.style.marginBottom = '2px';
    helperList.appendChild(typeSpan);
  });
  
  helperContainer.appendChild(helperList);
  
  // Insert after accept input description
  const acceptRow = acceptInput.closest('tr');
  const descriptionP = acceptRow.querySelector('p.description');
  if (descriptionP) {
    descriptionP.parentNode.insertBefore(helperContainer, descriptionP.nextSibling);
  }
}

// Add validation for tag generator inputs
function addInputValidation(panel) {
  const maxFilesInput = panel.querySelector('input[name="max-files"]');
  const maxSizeInput = panel.querySelector('input[name="max-size"]');
  const nameInput = panel.querySelector('input[name="name"]');
  
  // Validate max files
  if (maxFilesInput) {
    maxFilesInput.addEventListener('input', function() {
      const value = parseInt(this.value);
      if (value < 1) this.value = 1;
      if (value > 50) this.value = 50;
    });
  }
  
  // Validate max size format
  if (maxSizeInput) {
    maxSizeInput.addEventListener('blur', function() {
      const value = this.value.trim();
      if (value && !value.match(/^\d+(\.\d+)?\s*(B|KB|MB|GB)$/i)) {
        this.style.borderColor = '#dc3232';
        this.title = 'Invalid format. Use: 10MB, 5MB, 2048KB, etc.';
      } else {
        this.style.borderColor = '';
        this.title = '';
      }
    });
  }
  
  // Validate name field
  if (nameInput) {
    nameInput.addEventListener('input', function() {
      const value = this.value.trim();
      if (value && !value.match(/^[a-zA-Z][a-zA-Z0-9_-]*$/)) {
        this.style.borderColor = '#dc3232';
        this.title = 'Name must start with a letter and contain only letters, numbers, hyphens, and underscores.';
      } else {
        this.style.borderColor = '';
        this.title = '';
      }
    });
  }
}

// Initialize when CF7 tag generator is opened
document.addEventListener('click', function(e) {
  if (e.target && e.target.textContent && e.target.textContent.includes('Drag & Drop Upload')) {
    setTimeout(() => {
      const panel = document.querySelector('#tag-generator-panel-dragdrop-upload');
      if (panel) {
        addInputValidation(panel);
      }
    }, 100);
  }
});
