<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Conditional Fields + Drag & Drop Integration Test</title>
    <link rel="stylesheet" href="../assets/css/admin-style.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f0f0f1;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        
        .form-field {
            margin-bottom: 20px;
        }
        
        .form-field label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #374151;
        }
        
        .form-field input[type="text"],
        .form-field input[type="email"],
        .form-field textarea,
        .form-field select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        
        .form-field input[type="radio"] {
            margin-right: 8px;
        }
        
        .radio-group {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
        }
        
        .radio-option {
            display: flex;
            align-items: center;
        }
        
        .test-info {
            background-color: #eff6ff;
            border: 1px solid #bfdbfe;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        
        .test-info h3 {
            margin: 0 0 10px 0;
            color: #1e40af;
            font-size: 16px;
        }
        
        .submit-button {
            background-color: #3b82f6;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
        }
        
        .submit-button:hover {
            background-color: #2563eb;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔄 Conditional Fields + Drag & Drop Integration Test</h1>
        
        <div class="test-info">
            <h3>Test Scenario:</h3>
            <p>This form demonstrates how drag-and-drop upload fields work with conditional logic:</p>
            <ul>
                <li><strong>Business Application:</strong> Shows document upload when "Business" is selected</li>
                <li><strong>Personal Application:</strong> Shows photo upload when "Personal" is selected</li>
                <li><strong>Support Request:</strong> Shows file attachment when "Support" is selected</li>
            </ul>
        </div>

        <form class="wpcf7-form" data-conditional-logic-initialized="false">
            <input type="hidden" name="_wpcf7" value="456">
            
            <!-- Basic Information -->
            <div class="form-field">
                <label for="applicant-name">Full Name *</label>
                <input type="text" id="applicant-name" name="applicant-name" required>
            </div>
            
            <div class="form-field">
                <label for="applicant-email">Email Address *</label>
                <input type="email" id="applicant-email" name="applicant-email" required>
            </div>
            
            <!-- Application Type (Conditional Trigger) -->
            <div class="form-field">
                <label>Application Type *</label>
                <div class="radio-group">
                    <div class="radio-option">
                        <input type="radio" id="type-business" name="application-type" value="business">
                        <label for="type-business">Business Application</label>
                    </div>
                    <div class="radio-option">
                        <input type="radio" id="type-personal" name="application-type" value="personal">
                        <label for="type-personal">Personal Application</label>
                    </div>
                    <div class="radio-option">
                        <input type="radio" id="type-support" name="application-type" value="support">
                        <label for="type-support">Support Request</label>
                    </div>
                </div>
            </div>
            
            <!-- Business Documents (Conditional Field 1) -->
            <div class="form-field la-addons-hidden" id="business-documents-field">
                <label>Business Documents</label>
                <p style="color: #6b7280; font-size: 14px; margin-bottom: 10px;">
                    Please upload your business registration, tax documents, and financial statements.
                </p>
                <div class="la-addons-dragdrop-upload" 
                     data-form-id="456" 
                     data-field-name="business-documents"
                     data-max-files="5"
                     data-max-size="10MB"
                     data-accepted-types="application/pdf,.doc,.docx,.xls,.xlsx">
                    
                    <div class="la-dragdrop-zone">
                        <div class="la-dragdrop-content">
                            <div class="la-dragdrop-icon">
                                <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                                </svg>
                            </div>
                            <div class="la-dragdrop-text">
                                <p class="la-dragdrop-primary">Drop business documents here or <button type="button" class="la-dragdrop-browse">browse</button></p>
                                <p class="la-dragdrop-secondary">PDF, DOC, XLS files only, maximum 5 files, 10MB each</p>
                            </div>
                        </div>
                        <input type="file" name="business-documents[]" multiple accept="application/pdf,.doc,.docx,.xls,.xlsx" style="display: none;">
                    </div>
                    <div class="la-dragdrop-files"></div>
                    <div class="la-dragdrop-progress" style="display: none;">
                        <div class="la-progress-bar">
                            <div class="la-progress-fill"></div>
                        </div>
                        <div class="la-progress-text">Uploading...</div>
                    </div>
                </div>
            </div>
            
            <!-- Personal Photos (Conditional Field 2) -->
            <div class="form-field la-addons-hidden" id="personal-photos-field">
                <label>Personal Photos</label>
                <p style="color: #6b7280; font-size: 14px; margin-bottom: 10px;">
                    Please upload your ID photo and any supporting images.
                </p>
                <div class="la-addons-dragdrop-upload" 
                     data-form-id="456" 
                     data-field-name="personal-photos"
                     data-max-files="3"
                     data-max-size="5MB"
                     data-accepted-types="image/*">
                    
                    <div class="la-dragdrop-zone">
                        <div class="la-dragdrop-content">
                            <div class="la-dragdrop-icon">
                                <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                                    <circle cx="8.5" cy="8.5" r="1.5"/>
                                    <polyline points="21,15 16,10 5,21"/>
                                </svg>
                            </div>
                            <div class="la-dragdrop-text">
                                <p class="la-dragdrop-primary">Drop photos here or <button type="button" class="la-dragdrop-browse">browse</button></p>
                                <p class="la-dragdrop-secondary">Images only, maximum 3 files, 5MB each</p>
                            </div>
                        </div>
                        <input type="file" name="personal-photos[]" multiple accept="image/*" style="display: none;">
                    </div>
                    <div class="la-dragdrop-files"></div>
                    <div class="la-dragdrop-progress" style="display: none;">
                        <div class="la-progress-bar">
                            <div class="la-progress-fill"></div>
                        </div>
                        <div class="la-progress-text">Uploading...</div>
                    </div>
                </div>
            </div>
            
            <!-- Support Attachments (Conditional Field 3) -->
            <div class="form-field la-addons-hidden" id="support-attachments-field">
                <label>Support Attachments</label>
                <p style="color: #6b7280; font-size: 14px; margin-bottom: 10px;">
                    Please attach screenshots, logs, or any files related to your support request.
                </p>
                <div class="la-addons-dragdrop-upload" 
                     data-form-id="456" 
                     data-field-name="support-attachments"
                     data-max-files="10"
                     data-max-size="20MB"
                     data-accepted-types="image/*,application/pdf,.txt,.log,.zip,.rar">
                    
                    <div class="la-dragdrop-zone">
                        <div class="la-dragdrop-content">
                            <div class="la-dragdrop-icon">
                                <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M21.44 11.05l-9.19 9.19a6 6 0 0 1-8.49-8.49l9.19-9.19a4 4 0 0 1 5.66 5.66l-9.2 9.19a2 2 0 0 1-2.83-2.83l8.49-8.48"/>
                                </svg>
                            </div>
                            <div class="la-dragdrop-text">
                                <p class="la-dragdrop-primary">Drop support files here or <button type="button" class="la-dragdrop-browse">browse</button></p>
                                <p class="la-dragdrop-secondary">Images, PDFs, logs, archives, maximum 10 files, 20MB each</p>
                            </div>
                        </div>
                        <input type="file" name="support-attachments[]" multiple accept="image/*,application/pdf,.txt,.log,.zip,.rar" style="display: none;">
                    </div>
                    <div class="la-dragdrop-files"></div>
                    <div class="la-dragdrop-progress" style="display: none;">
                        <div class="la-progress-bar">
                            <div class="la-progress-fill"></div>
                        </div>
                        <div class="la-progress-text">Uploading...</div>
                    </div>
                </div>
            </div>
            
            <!-- Additional Message -->
            <div class="form-field">
                <label for="additional-message">Additional Message</label>
                <textarea id="additional-message" name="additional-message" rows="4" placeholder="Please provide any additional information..."></textarea>
            </div>
            
            <button type="submit" class="submit-button">Submit Application</button>
        </form>
    </div>

    <!-- Mock data and scripts -->
    <script>
        // Mock WordPress AJAX data
        window.laAddonsDragDrop = {
            ajaxUrl: '/wp-admin/admin-ajax.php',
            nonce: 'test-nonce-456',
            maxFileSize: 20971520, // 20MB
            allowedTypes: {}
        };
        
        // Mock conditional logic data
        window.laAddonsConditionalLogic = {
            ajaxUrl: '/wp-admin/admin-ajax.php',
            nonce: 'conditional-nonce-456',
            restUrl: '/wp-json/',
            restNonce: 'rest-nonce-456'
        };
        
        // Mock conditional logic rules
        const mockConditionalRules = [
            {
                target_field: 'business-documents-field',
                action: 'show',
                logic: 'AND',
                conditions: [
                    {
                        field: 'application-type',
                        operator: 'equals',
                        value: 'business'
                    }
                ]
            },
            {
                target_field: 'personal-photos-field',
                action: 'show',
                logic: 'AND',
                conditions: [
                    {
                        field: 'application-type',
                        operator: 'equals',
                        value: 'personal'
                    }
                ]
            },
            {
                target_field: 'support-attachments-field',
                action: 'show',
                logic: 'AND',
                conditions: [
                    {
                        field: 'application-type',
                        operator: 'equals',
                        value: 'support'
                    }
                ]
            }
        ];
        
        // Simple conditional logic implementation for testing
        function initTestConditionalLogic() {
            const form = document.querySelector('.wpcf7-form');
            const radioInputs = form.querySelectorAll('input[name="application-type"]');
            
            function evaluateConditions() {
                const selectedValue = form.querySelector('input[name="application-type"]:checked')?.value;
                
                // Hide all conditional fields first
                document.getElementById('business-documents-field').classList.add('la-addons-hidden');
                document.getElementById('personal-photos-field').classList.add('la-addons-hidden');
                document.getElementById('support-attachments-field').classList.add('la-addons-hidden');
                
                // Show relevant field based on selection
                if (selectedValue === 'business') {
                    document.getElementById('business-documents-field').classList.remove('la-addons-hidden');
                } else if (selectedValue === 'personal') {
                    document.getElementById('personal-photos-field').classList.remove('la-addons-hidden');
                } else if (selectedValue === 'support') {
                    document.getElementById('support-attachments-field').classList.remove('la-addons-hidden');
                }
            }
            
            // Add event listeners
            radioInputs.forEach(input => {
                input.addEventListener('change', evaluateConditions);
            });
            
            // Initial evaluation
            evaluateConditions();
        }
        
        // Initialize when DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
            initTestConditionalLogic();
        });
    </script>
    
    <script src="../assets/js/drag-drop-upload.js"></script>
</body>
</html>
