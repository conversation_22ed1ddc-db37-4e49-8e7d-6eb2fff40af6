<?php
/**
 * Test file for Conditional Fields Backend Functionality
 * 
 * This file can be run from the command line or included in a WordPress test environment
 * to verify that the conditional fields backend functionality is working correctly.
 */

// Mock WordPress functions for testing outside WordPress environment
if (!function_exists('get_post_meta')) {
    function get_post_meta($post_id, $key, $single = false) {
        // Mock data for testing
        if ($key === '_la_addons_conditional_logic') {
            return array(
                array(
                    'target_field' => 'conditional-field',
                    'action' => 'show',
                    'logic' => 'all',
                    'conditions' => array(
                        array(
                            'field' => 'trigger-field',
                            'operator' => 'equal',
                            'value' => 'yes'
                        )
                    )
                )
            );
        }
        return false;
    }
}

if (!function_exists('sanitize_text_field')) {
    function sanitize_text_field($str) {
        return trim(strip_tags($str));
    }
}

if (!function_exists('error_log')) {
    function error_log($message) {
        echo "[LOG] " . $message . "\n";
    }
}

// Include the conditional fields API file
require_once dirname(__DIR__) . '/includes/conditional-fields-api.php';

/**
 * Test the field scanning functionality
 */
function test_field_scanning() {
    echo "Testing field scanning functionality...\n";
    
    // Mock form content
    $form_content = '[text* your-name] [email* your-email] [select menu-123 "Option 1|Option 2|Option 3"] [submit "Send"]';
    
    // Test the regex pattern
    preg_match_all('/\[([a-zA-Z0-9_*-]+)(?:\*)?(?:\s+([^\]]*))?\]/', $form_content, $matches, PREG_SET_ORDER);
    
    echo "Found " . count($matches) . " form tags\n";
    
    foreach ($matches as $match) {
        echo "Tag: " . $match[1] . " | Attributes: " . (isset($match[2]) ? $match[2] : 'none') . "\n";
    }
    
    return count($matches) > 0;
}

/**
 * Test conditional logic data structure
 */
function test_conditional_logic_structure() {
    echo "\nTesting conditional logic data structure...\n";
    
    $test_rules = array(
        array(
            'target_field' => 'conditional-field',
            'action' => 'show',
            'logic' => 'all',
            'conditions' => array(
                array(
                    'field' => 'trigger-field',
                    'operator' => 'equal',
                    'value' => 'yes'
                )
            )
        )
    );
    
    // Test structure validation
    $valid = true;
    foreach ($test_rules as $rule) {
        if (!isset($rule['target_field']) || !isset($rule['action']) || !isset($rule['logic']) || !isset($rule['conditions'])) {
            $valid = false;
            echo "Invalid rule structure\n";
            break;
        }
        
        foreach ($rule['conditions'] as $condition) {
            if (!isset($condition['field']) || !isset($condition['operator']) || !isset($condition['value'])) {
                $valid = false;
                echo "Invalid condition structure\n";
                break;
            }
        }
    }
    
    if ($valid) {
        echo "Conditional logic structure is valid\n";
    }
    
    return $valid;
}

/**
 * Test API endpoint response format
 */
function test_api_response_format() {
    echo "\nTesting API response format...\n";
    
    // Mock request object
    $request = array('id' => 123);
    
    // Test the API function
    if (function_exists('la_addons_get_conditional_logic_endpoint')) {
        // This would normally be called by WordPress REST API
        echo "API endpoint function exists\n";
        return true;
    } else {
        echo "API endpoint function not found\n";
        return false;
    }
}

/**
 * Test data sanitization
 */
function test_data_sanitization() {
    echo "\nTesting data sanitization...\n";
    
    $test_data = array(
        'target_field' => '<script>alert("xss")</script>test-field',
        'action' => 'show',
        'logic' => 'all',
        'conditions' => array(
            array(
                'field' => '<script>alert("xss")</script>trigger-field',
                'operator' => 'equal',
                'value' => '<script>alert("xss")</script>yes'
            )
        )
    );
    
    // Sanitize the data
    $sanitized = array(
        'target_field' => sanitize_text_field($test_data['target_field']),
        'action' => in_array($test_data['action'], array('show', 'hide')) ? $test_data['action'] : 'show',
        'logic' => in_array($test_data['logic'], array('all', 'any')) ? $test_data['logic'] : 'all',
        'conditions' => array()
    );
    
    foreach ($test_data['conditions'] as $condition) {
        $sanitized['conditions'][] = array(
            'field' => sanitize_text_field($condition['field']),
            'operator' => sanitize_text_field($condition['operator']),
            'value' => sanitize_text_field($condition['value'])
        );
    }
    
    // Check if XSS was removed
    $xss_removed = (
        strpos($sanitized['target_field'], '<script>') === false &&
        strpos($sanitized['conditions'][0]['field'], '<script>') === false &&
        strpos($sanitized['conditions'][0]['value'], '<script>') === false
    );
    
    if ($xss_removed) {
        echo "Data sanitization working correctly\n";
    } else {
        echo "Data sanitization failed\n";
    }
    
    return $xss_removed;
}

/**
 * Test operator validation
 */
function test_operator_validation() {
    echo "\nTesting operator validation...\n";
    
    $valid_operators = array(
        'equal', 'not_equal', 'contains', 'not_contains',
        'greater_than', 'less_than', 'starts_with', 'ends_with',
        'is_empty', 'is_not_empty'
    );
    
    $test_operators = array('equal', 'invalid_operator', 'contains', 'xyz');
    
    $all_valid = true;
    foreach ($test_operators as $operator) {
        $is_valid = in_array($operator, $valid_operators);
        echo "Operator '$operator': " . ($is_valid ? 'valid' : 'invalid') . "\n";
        if (!$is_valid && in_array($operator, array('equal', 'contains'))) {
            $all_valid = false;
        }
    }
    
    return $all_valid;
}

/**
 * Run all tests
 */
function run_all_tests() {
    echo "=== Conditional Fields Backend Tests ===\n\n";
    
    $tests = array(
        'Field Scanning' => 'test_field_scanning',
        'Conditional Logic Structure' => 'test_conditional_logic_structure',
        'API Response Format' => 'test_api_response_format',
        'Data Sanitization' => 'test_data_sanitization',
        'Operator Validation' => 'test_operator_validation'
    );
    
    $passed = 0;
    $total = count($tests);
    
    foreach ($tests as $test_name => $test_function) {
        echo "Running test: $test_name\n";
        $result = call_user_func($test_function);
        
        if ($result) {
            echo "✓ PASSED\n\n";
            $passed++;
        } else {
            echo "✗ FAILED\n\n";
        }
    }
    
    echo "=== Test Summary ===\n";
    echo "Passed: $passed/$total\n";
    echo "Failed: " . ($total - $passed) . "/$total\n";
    
    if ($passed === $total) {
        echo "All tests passed! ✓\n";
    } else {
        echo "Some tests failed. Please review the output above.\n";
    }
}

// Run tests if this file is executed directly
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    run_all_tests();
}
