<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Conditional Fields Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-form {
            background: #f9f9f9;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .form-field {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
        .la-addons-hidden {
            display: none !important;
            visibility: hidden !important;
        }
        .la-addons-visible {
            display: block !important;
            visibility: visible !important;
        }
        .test-results {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
        }
        .error {
            background: #ffe8e8;
            color: #d00;
        }
        .success {
            background: #e8f5e8;
            color: #080;
        }
    </style>
</head>
<body>
    <h1>Conditional Fields Test</h1>
    
    <div class="test-form">
        <h2>Test Form</h2>
        <form class="wpcf7-form" id="test-form">
            <input type="hidden" name="_wpcf7" value="123">
            
            <div class="form-field">
                <label for="trigger-field">Trigger Field (Select "Yes" to show hidden field):</label>
                <select name="trigger-field" id="trigger-field">
                    <option value="">-- Select --</option>
                    <option value="yes">Yes</option>
                    <option value="no">No</option>
                </select>
            </div>
            
            <div class="form-field wpcf7-form-control-wrap" id="conditional-field-wrapper">
                <p>
                    <label for="conditional-field">Conditional Field (Should show when "Yes" is selected):</label>
                    <input type="text" name="conditional-field" id="conditional-field" placeholder="This field is conditionally shown">
                </p>
            </div>
            
            <div class="form-field">
                <label for="always-visible">Always Visible Field:</label>
                <input type="text" name="always-visible" id="always-visible" placeholder="This field is always visible">
            </div>
        </form>
    </div>
    
    <div class="test-results" id="test-results">
        <h3>Test Results</h3>
        <div id="results-content">
            <p>Run tests to see results...</p>
        </div>
    </div>
    
    <button onclick="runTests()">Run Tests</button>
    <button onclick="clearResults()">Clear Results</button>
    
    <script>
        // Mock WordPress REST API and CF7 objects
        window.wpcf7 = {
            api: {
                root: '/wp-json/',
                nonce: 'test-nonce'
            }
        };
        
        // Mock fetch for testing
        const originalFetch = window.fetch;
        window.fetch = function(url, options) {
            console.log('Mock fetch called:', url, options);
            
            if (url.includes('conditional-logic')) {
                return Promise.resolve({
                    ok: true,
                    json: () => Promise.resolve({
                        success: true,
                        data: {
                            conditionalLogic: [
                                {
                                    target_field: 'conditional-field',
                                    action: 'show',
                                    logic: 'all',
                                    conditions: [
                                        {
                                            field: 'trigger-field',
                                            operator: 'equal',
                                            value: 'yes'
                                        }
                                    ]
                                }
                            ]
                        }
                    })
                });
            }
            
            return originalFetch.apply(this, arguments);
        };
        
        // Test functions
        function runTests() {
            const results = document.getElementById('results-content');
            results.innerHTML = '<h4>Running Tests...</h4>';
            
            const tests = [
                testInitialState,
                testShowCondition,
                testHideCondition,
                testMultipleConditions
            ];
            
            let passed = 0;
            let failed = 0;
            
            tests.forEach((test, index) => {
                try {
                    const result = test();
                    if (result.success) {
                        passed++;
                        results.innerHTML += `<p class="success">✓ Test ${index + 1}: ${result.message}</p>`;
                    } else {
                        failed++;
                        results.innerHTML += `<p class="error">✗ Test ${index + 1}: ${result.message}</p>`;
                    }
                } catch (error) {
                    failed++;
                    results.innerHTML += `<p class="error">✗ Test ${index + 1}: Error - ${error.message}</p>`;
                }
            });
            
            results.innerHTML += `<h4>Summary: ${passed} passed, ${failed} failed</h4>`;
        }
        
        function testInitialState() {
            const conditionalField = document.getElementById('conditional-field-wrapper');
            const triggerField = document.getElementById('trigger-field');
            
            // Reset form
            triggerField.value = '';
            
            // Simulate initial evaluation
            if (conditionalField.style.display === 'none' || conditionalField.classList.contains('la-addons-hidden')) {
                return { success: true, message: 'Initial state: conditional field is hidden' };
            } else {
                return { success: false, message: 'Initial state: conditional field should be hidden' };
            }
        }
        
        function testShowCondition() {
            const conditionalField = document.getElementById('conditional-field-wrapper');
            const triggerField = document.getElementById('trigger-field');
            
            // Set trigger to "yes"
            triggerField.value = 'yes';
            triggerField.dispatchEvent(new Event('change'));
            
            // Check if field is shown
            setTimeout(() => {
                if (conditionalField.style.display !== 'none' && !conditionalField.classList.contains('la-addons-hidden')) {
                    return { success: true, message: 'Show condition: conditional field is visible when trigger is "yes"' };
                } else {
                    return { success: false, message: 'Show condition: conditional field should be visible when trigger is "yes"' };
                }
            }, 100);
            
            return { success: true, message: 'Show condition test initiated' };
        }
        
        function testHideCondition() {
            const conditionalField = document.getElementById('conditional-field-wrapper');
            const triggerField = document.getElementById('trigger-field');
            
            // Set trigger to "no"
            triggerField.value = 'no';
            triggerField.dispatchEvent(new Event('change'));
            
            // Check if field is hidden
            setTimeout(() => {
                if (conditionalField.style.display === 'none' || conditionalField.classList.contains('la-addons-hidden')) {
                    return { success: true, message: 'Hide condition: conditional field is hidden when trigger is "no"' };
                } else {
                    return { success: false, message: 'Hide condition: conditional field should be hidden when trigger is "no"' };
                }
            }, 100);
            
            return { success: true, message: 'Hide condition test initiated' };
        }
        
        function testMultipleConditions() {
            // This would test more complex scenarios with multiple conditions
            return { success: true, message: 'Multiple conditions test (placeholder)' };
        }
        
        function clearResults() {
            document.getElementById('results-content').innerHTML = '<p>Run tests to see results...</p>';
        }
        
        // Initialize conditional logic when page loads
        document.addEventListener('DOMContentLoaded', function() {
            // Simulate the conditional logic initialization
            console.log('Test page loaded, initializing conditional logic...');
            
            // Hide conditional field initially
            const conditionalField = document.getElementById('conditional-field-wrapper');
            conditionalField.style.display = 'none';
            conditionalField.classList.add('la-addons-hidden');
        });
    </script>
</body>
</html>
