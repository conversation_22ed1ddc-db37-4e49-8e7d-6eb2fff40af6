<?php
/**
 * Plugin Name: Contact Form 7 Signature Add-on
 * Description: Adds a digital signature field to Contact Form 7.
 * Version: 1.0
 * Author: <PERSON><PERSON>
 */

// Block direct access to the file
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Defines the handler for the [signature] form tag.
 * This function generates the HTML for the signature pad on the front-end.
 */
function la_addons_signature_form_tag_handler( $tag ) {
    $tag = new WPCF7_FormTag( $tag );

    // Ensure a name is set for the field
    if ( empty( $tag->name ) ) {
        return '';
    }

    $class = wpcf7_form_controls_class( $tag->type, 'la-addons-signature-pad' );
    $atts = array();
    $atts['class'] = $tag->get_class_option( $class );
    $atts['id'] = $tag->get_id_option();
    $atts['tabindex'] = $tag->get_option( 'tabindex', 'int', true );
    $atts['data-form-id'] = wpcf7_get_current_contact_form()->id(); // Add form ID as data attribute

    $value = (string) reset( $tag->values );

    if ( $tag->has_option( 'placeholder' ) ) {
        $atts['placeholder'] = $tag->get_option( 'placeholder' );
    }

    $atts = wpcf7_format_atts( $atts );

    // The HTML includes a canvas for drawing and a hidden input to store the signature data
    $html = sprintf(
        '<div %1$s><canvas style="border: 1px solid #ccc;"></canvas><input type="hidden" name="%2$s" value="%3$s" /></div>',
        $atts,
        $tag->name,
        esc_attr( $value )
    );

    return $html;
}

/**
 * Registers the new form tag with Contact Form 7.
 * This function is hooked to `wpcf7_init` to ensure it runs on both frontend and backend.
 */
function la_addons_register_signature_form_tag() {
    if ( function_exists( 'wpcf7_add_form_tag' ) ) {
        wpcf7_add_form_tag(
            'signature',
            'la_addons_signature_form_tag_handler',
            array( 'name-attr' => true )
        );
    }
}
add_action( 'wpcf7_init', 'la_addons_register_signature_form_tag', 10, 0 );

/**
 * Defines the popup modal for the tag generator in the form editor.
 * This function creates the interface for adding the signature tag.
 */
function la_addons_signature_tag_generator_callback( $contact_form, $args = '' ) {
    $args = wp_parse_args( $args, array() );
    $description = __( "Generate a form tag for a digital signature pad. Users can draw their signature directly on the form.", 'la-addons' );

    ?>
    <div class="control-box">
        <fieldset>
            <legend><?php echo esc_html( $description ); ?></legend>
            <table class="form-table">
                <tbody>
                    <tr>
                        <th scope="row"><label for="<?php echo esc_attr( $args['content'] . '-name' ); ?>"><?php echo esc_html( __( 'Name', 'la-addons' ) ); ?></label></th>
                        <td><input type="text" name="name" class="tg-name oneline" /></td>
                    </tr>
                </tbody>
            </table>
        </fieldset>
    </div>

    <div class="insert-box">
        <input type="text" name="signature" class="tag code" readonly="readonly" onfocus="this.select()" />

        <div class="submitbox">
            <input type="button" class="button button-primary insert-tag" value="<?php echo esc_attr( __( 'Insert Tag', 'la-addons' ) ); ?>" />
        </div>
    </div>
    <?php
}

/**
 * Registers the tag generator with Contact Form 7 for the admin area.
 * This function is hooked to `wpcf7_admin_init`.
 */
function la_addons_add_signature_tag_generator() {
    if ( function_exists( 'wpcf7_add_tag_generator' ) ) {
        wpcf7_add_tag_generator(
            'signature',
            __( 'Signature', 'la-addons' ), // This is the button text
            'la_addons_signature_tag_generator_callback',
            array( 'title' => __( 'Signature Pad', 'la-addons' ) )
        );
    }
}
add_action( 'wpcf7_admin_init', 'la_addons_add_signature_tag_generator', 20, 0 );

