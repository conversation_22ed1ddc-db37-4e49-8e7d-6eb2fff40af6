<?php
/**
 * Digital Signature Options UI
 */

function la_addons_debug_signature_options() {
    if (isset($_GET['debug']) && $_GET['debug'] === 'signature') {
        error_log('Signature Options Debug: Checking if hooks are registered');
        add_action('admin_notices', function() {
            echo '<div class="notice notice-info"><p>Signature Options Debug: Hooks are registered</p></div>';
        });
    }
}
add_action('admin_init', 'la_addons_debug_signature_options');

function la_addons_add_signature_options_tab( $panels ) {
    error_log('la_addons_add_signature_options_tab called. Current panels: ' . print_r($panels, true));
    $panels['signature-options-panel'] = array(
        'title' => __( 'Signature Options', 'la-addons' ),
        'callback' => 'la_addons_signature_options_panel_content'
    );
    error_log('Panels after adding signature options: ' . print_r($panels, true));
    return $panels;
}

// Increase priority to ensure it loads after CF7's default panels
add_filter( 'wpcf7_editor_panels', 'la_addons_add_signature_options_tab', 20 );

function la_addons_signature_options_panel_content( $post ) {
    $options = get_post_meta( $post->id(), '_la_addons_signature_options', true );
    
    // Set defaults if no options exist
    if ( empty( $options ) ) {
        $options = array(
            'pad_bg_color' => '#dddddd',
            'pen_color' => '#000000',
            'pad_width' => '300',
            'pad_height' => '100'
        );
    }
    ?>
    <h2><?php echo esc_html__( 'Signature Options', 'la-addons' ); ?></h2>
    <p><?php echo esc_html__( 'Customize the appearance of signature fields in this form.', 'la-addons' ); ?></p>
    
    <div class="signature-options-wrapper">
        <div class="signature-option-row">
            <div class="signature-option-col">
                <label for="signature-pad-bg-color"><?php echo esc_html__( 'Signature Pad Background Color', 'la-addons' ); ?></label>
                <input type="color" id="signature-pad-bg-color" name="la_addons_signature_options[pad_bg_color]" value="<?php echo esc_attr( $options['pad_bg_color'] ); ?>">
                <p class="description"><?php echo esc_html__( 'E.g. Default is #dddddd', 'la-addons' ); ?></p>
            </div>
            
            <div class="signature-option-col">
                <label for="signature-pen-color"><?php echo esc_html__( 'Signature Pen Color', 'la-addons' ); ?></label>
                <input type="color" id="signature-pen-color" name="la_addons_signature_options[pen_color]" value="<?php echo esc_attr( $options['pen_color'] ); ?>">
                <p class="description"><?php echo esc_html__( 'E.g. Default is #000000', 'la-addons' ); ?></p>
            </div>
        </div>
        
        <div class="signature-option-row">
            <div class="signature-option-col">
                <label for="signature-pad-width"><?php echo esc_html__( 'Signature Pad Width', 'la-addons' ); ?></label>
                <input type="number" id="signature-pad-width" name="la_addons_signature_options[pad_width]" value="<?php echo esc_attr( $options['pad_width'] ); ?>">
                <p class="description"><?php echo esc_html__( 'E.g. There is no need to include units such as "px" or "rem".', 'la-addons' ); ?></p>
            </div>
            
            <div class="signature-option-col">
                <label for="signature-pad-height"><?php echo esc_html__( 'Signature Pad Height', 'la-addons' ); ?></label>
                <input type="number" id="signature-pad-height" name="la_addons_signature_options[pad_height]" value="<?php echo esc_attr( $options['pad_height'] ); ?>">
                <p class="description"><?php echo esc_html__( 'E.g. There is no need to include units such as "px" or "rem".', 'la-addons' ); ?></p>
            </div>
        </div>
    </div>
    <?php
}

function la_addons_save_signature_options( $post ) {
    if ( isset( $_POST['la_addons_signature_options'] ) ) {
        $options = $_POST['la_addons_signature_options'];
        
        // Ensure numeric values are properly formatted
        if (isset($options['pad_width'])) {
            $options['pad_width'] = intval($options['pad_width']);
        }
        
        if (isset($options['pad_height'])) {
            $options['pad_height'] = intval($options['pad_height']);
        }
        
        error_log('Saving signature options: ' . print_r($options, true));
        update_post_meta( $post->id(), '_la_addons_signature_options', $options );
    }
}

add_action( 'wpcf7_save_contact_form', 'la_addons_save_signature_options' );





