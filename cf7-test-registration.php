<?php
/**
 * Temporary CF7 Tag Registration Test
 * Add this to your theme's functions.php to test CF7 tag registration
 */

// Test 1: Register on wp_loaded (latest possible hook)
add_action('wp_loaded', function() {
    error_log('CF7 Test: wp_loaded hook fired');
    
    if (function_exists('wpcf7_add_form_tag')) {
        error_log('CF7 Test: wpcf7_add_form_tag function exists on wp_loaded');
        
        wpcf7_add_form_tag('test-simple', function($tag) {
            error_log('CF7 Test: test-simple handler called');
            return '<div style="background: lime; padding: 15px; border: 2px solid green;">
                <strong>✅ CF7 Tag Registration Working!</strong>
                <br>This proves CF7 can process custom tags.
            </div>';
        });
        
        error_log('CF7 Test: test-simple tag registered on wp_loaded');
    } else {
        error_log('CF7 Test: wpcf7_add_form_tag function NOT available on wp_loaded');
    }
});

// Test 2: Register on wpcf7_init (CF7 specific hook)
add_action('wpcf7_init', function() {
    error_log('CF7 Test: wpcf7_init hook fired');
    
    if (function_exists('wpcf7_add_form_tag')) {
        error_log('CF7 Test: wpcf7_add_form_tag function exists on wpcf7_init');
        
        wpcf7_add_form_tag('test-cf7init', function($tag) {
            error_log('CF7 Test: test-cf7init handler called');
            return '<div style="background: cyan; padding: 15px; border: 2px solid blue;">
                <strong>✅ CF7 wpcf7_init Hook Working!</strong>
                <br>This tag was registered on wpcf7_init hook.
            </div>';
        });
        
        error_log('CF7 Test: test-cf7init tag registered on wpcf7_init');
    } else {
        error_log('CF7 Test: wpcf7_add_form_tag function NOT available on wpcf7_init');
    }
});

// Test 3: Check CF7 version and status
add_action('init', function() {
    error_log('CF7 Test: init hook fired');
    
    if (defined('WPCF7_VERSION')) {
        error_log('CF7 Test: Contact Form 7 version: ' . WPCF7_VERSION);
    } else {
        error_log('CF7 Test: WPCF7_VERSION not defined');
    }
    
    if (class_exists('WPCF7_ContactForm')) {
        error_log('CF7 Test: WPCF7_ContactForm class exists');
    } else {
        error_log('CF7 Test: WPCF7_ContactForm class NOT exists');
    }
    
    if (function_exists('wpcf7_add_form_tag')) {
        error_log('CF7 Test: wpcf7_add_form_tag function exists on init');
    } else {
        error_log('CF7 Test: wpcf7_add_form_tag function NOT exists on init');
    }
});

// Test 4: Very late registration
add_action('wp', function() {
    error_log('CF7 Test: wp hook fired (very late)');
    
    if (function_exists('wpcf7_add_form_tag')) {
        error_log('CF7 Test: wpcf7_add_form_tag function exists on wp hook');
        
        wpcf7_add_form_tag('test-late', function($tag) {
            error_log('CF7 Test: test-late handler called');
            return '<div style="background: orange; padding: 15px; border: 2px solid red;">
                <strong>✅ Late Registration Working!</strong>
                <br>This tag was registered on wp hook (very late).
            </div>';
        });
        
        error_log('CF7 Test: test-late tag registered on wp hook');
    } else {
        error_log('CF7 Test: wpcf7_add_form_tag function NOT available on wp hook');
    }
});

// Test 5: Check if our dragdrop handler function exists
add_action('wp_loaded', function() {
    if (function_exists('la_addons_dragdrop_upload_form_tag_handler')) {
        error_log('CF7 Test: la_addons_dragdrop_upload_form_tag_handler function EXISTS');
    } else {
        error_log('CF7 Test: la_addons_dragdrop_upload_form_tag_handler function NOT EXISTS');
    }
});
